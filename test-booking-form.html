<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Form Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }

        .booking-form-section {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            max-width: 320px;
            margin: 0 auto;
        }

        .booking-form-header {
            text-align: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .from-label {
            display: block;
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .price-display {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }

        .booking-form {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group-row {
            display: flex !important;
            gap: 0.75rem;
        }

        .form-group-row .form-group {
            flex: 1 !important;
        }

        .form-group label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .quantity-selector {
            display: flex;
            align-items: center;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            overflow: hidden;
        }

        .qty-btn {
            background: #f9fafb;
            border: none;
            padding: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            transition: background-color 0.2s ease;
            min-width: 35px;
        }

        .qty-btn:hover {
            background: #e5e7eb;
        }

        .qty-input {
            border: none;
            text-align: center;
            font-weight: 600;
            padding: 0.5rem 0.25rem;
            min-width: 50px;
            background: white;
        }

        .age-note {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .guest-names {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .guest-name-row {
            display: flex;
            gap: 0.5rem;
        }

        .title-select {
            flex: 0 0 70px;
            padding: 0.5rem 0.25rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.75rem;
            background: white;
        }

        .guest-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .book-now-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 1rem;
        }

        .book-now-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body>
    <div class="booking-form-section">
        <div class="booking-form-header">
            <span class="from-label">FROM</span>
            <div class="price-display" id="totalPrice">500.00 GEL</div>
        </div>

        <form class="booking-form" id="bookingForm">
            <!-- Date Field -->
            <div class="form-group">
                <label for="booking-date">Date</label>
                <input type="date" id="booking-date" name="booking-date" class="form-input" required>
            </div>

            <!-- Adults and Children Fields -->
            <div class="form-group-row">
                <div class="form-group">
                    <label for="adults">Adults</label>
                    <div class="quantity-selector">
                        <button type="button" class="qty-btn minus" data-target="adults">-</button>
                        <input type="number" id="adults" name="adults" value="2" min="1" max="4" class="qty-input" readonly>
                        <button type="button" class="qty-btn plus" data-target="adults">+</button>
                    </div>
                    <small class="age-note">Age 18+</small>
                </div>
                
                <div class="form-group">
                    <label for="children">Children</label>
                    <div class="quantity-selector">
                        <button type="button" class="qty-btn minus" data-target="children">-</button>
                        <input type="number" id="children" name="children" value="0" min="0" max="2" class="qty-input" readonly>
                        <button type="button" class="qty-btn plus" data-target="children">+</button>
                    </div>
                    <small class="age-note">Age 6-17</small>
                </div>
            </div>

            <!-- Guest Names -->
            <div class="form-group">
                <label>Guest names *</label>
                <div class="guest-names" id="guestNames">
                    <!-- Dynamic content -->
                </div>
            </div>

            <!-- Book Now Button -->
            <button type="submit" class="book-now-btn">Book Now</button>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const basePrice = 250;
            const adultsInput = document.getElementById('adults');
            const childrenInput = document.getElementById('children');
            const totalPriceDisplay = document.getElementById('totalPrice');
            const guestNamesContainer = document.getElementById('guestNames');
            
            console.log('Form elements:', {
                adultsInput, childrenInput, totalPriceDisplay, guestNamesContainer
            });
            
            function updatePrice() {
                const adults = parseInt(adultsInput.value) || 2;
                const children = parseInt(childrenInput.value) || 0;
                const totalPrice = (adults * basePrice) + (children * basePrice * 0.5);
                totalPriceDisplay.textContent = totalPrice.toFixed(2) + ' GEL';
                console.log('Price updated:', totalPrice);
            }
            
            function updateGuestNames() {
                const adults = parseInt(adultsInput.value) || 2;
                const children = parseInt(childrenInput.value) || 0;
                const totalGuests = adults + children;
                
                guestNamesContainer.innerHTML = '';
                
                for (let i = 0; i < totalGuests; i++) {
                    const guestRow = document.createElement('div');
                    guestRow.className = 'guest-name-row';
                    guestRow.innerHTML = `
                        <select class="title-select">
                            <option>Mr</option>
                            <option>Ms</option>
                            <option>Mrs</option>
                        </select>
                        <input type="text" placeholder="Guest name" class="guest-input" required>
                    `;
                    guestNamesContainer.appendChild(guestRow);
                }
                console.log('Guest names updated for', totalGuests, 'guests');
            }
            
            // Quantity buttons
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('qty-btn')) {
                    e.preventDefault();
                    
                    const target = e.target.getAttribute('data-target');
                    const input = document.getElementById(target);
                    const isPlus = e.target.classList.contains('plus');
                    
                    console.log('Button clicked:', target, isPlus);
                    
                    if (!input) return;
                    
                    const currentValue = parseInt(input.value) || 0;
                    const min = parseInt(input.getAttribute('min')) || 0;
                    const max = parseInt(input.getAttribute('max')) || 10;
                    
                    let newValue = currentValue;
                    if (isPlus && currentValue < max) {
                        newValue = currentValue + 1;
                    } else if (!isPlus && currentValue > min) {
                        newValue = currentValue - 1;
                    }
                    
                    // Check total people limit (4 max)
                    const adults = target === 'adults' ? newValue : parseInt(adultsInput.value) || 0;
                    const children = target === 'children' ? newValue : parseInt(childrenInput.value) || 0;
                    
                    if (adults + children <= 4) {
                        input.value = newValue;
                        updatePrice();
                        updateGuestNames();
                    }
                    
                    console.log('New value:', newValue);
                }
            });
            
            // Initialize
            updatePrice();
            updateGuestNames();
            
            // Form submission
            document.getElementById('bookingForm').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Booking submitted! Total: ' + totalPriceDisplay.textContent);
            });
        });
    </script>
</body>
</html>
