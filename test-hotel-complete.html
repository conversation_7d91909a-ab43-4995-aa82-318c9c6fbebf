<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Style Product - Complete Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }

        .hotel-booking-layout {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        /* Gallery Styles */
        .hotel-gallery-container {
            margin-bottom: 2rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .hotel-gallery-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4px;
            height: 400px;
        }

        .hotel-main-image {
            position: relative;
            overflow: hidden;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            cursor: pointer;
        }

        .hotel-main-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .hotel-thumbnails {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 4px;
        }

        .hotel-thumbnail {
            position: relative;
            overflow: hidden;
            cursor: pointer;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 0.75rem;
        }

        .hotel-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-zoom-icon {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .hotel-main-image:hover .image-zoom-icon,
        .hotel-thumbnail:hover .image-zoom-icon {
            opacity: 1;
        }

        /* Highlights Section */
        .highlights-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }

        .highlights-title {
            margin-bottom: 1rem;
        }

        .highlights-badge {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: #1f2937;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
        }

        /* Image Modal */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            justify-content: center;
            align-items: center;
        }

        .image-modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
        }

        .image-modal-content img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .image-modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            z-index: 10000;
        }

        .image-modal-close:hover {
            opacity: 0.7;
        }

        /* Content Layout */
        .hotel-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
            align-items: start;
        }

        .hotel-main-content {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .hotel-sidebar {
            position: sticky;
            top: 2rem;
        }

        /* Booking Form */
        .booking-form-section {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            max-width: 320px;
        }

        .booking-form-header {
            text-align: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .from-label {
            display: block;
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .price-display {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }

        .booking-form {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .hotel-form-row {
            display: flex !important;
            gap: 0.75rem;
        }

        .hotel-form-row .hotel-form-group {
            flex: 1 !important;
        }

        .hotel-form-group {
            display: flex;
            flex-direction: column;
        }

        .hotel-form-group label,
        .form-group label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .hotel-quantity-selector {
            display: flex;
            align-items: center;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            overflow: hidden;
        }

        .hotel-qty-btn {
            background: #f9fafb;
            border: none;
            padding: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            transition: background-color 0.2s ease;
            min-width: 35px;
        }

        .hotel-qty-btn:hover {
            background: #e5e7eb;
        }

        .hotel-qty-input {
            border: none;
            text-align: center;
            font-weight: 600;
            padding: 0.5rem 0.25rem;
            min-width: 50px;
            background: white;
        }

        .hotel-age-note {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .guest-names {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .guest-name-row {
            display: flex;
            gap: 0.5rem;
        }

        .title-select {
            flex: 0 0 70px;
            padding: 0.5rem 0.25rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.75rem;
            background: white;
        }

        .guest-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .book-now-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 1rem;
        }

        .book-now-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body>
    <div class="hotel-booking-layout">
        <!-- Hotel Gallery -->
        <div class="hotel-gallery-container">
            <div class="hotel-gallery-grid">
                <div class="hotel-main-image" onclick="openImageModal('https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1200')">
                    <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800" alt="Hotel Main Image">
                    <div class="image-zoom-icon">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
                
                <div class="hotel-thumbnails">
                    <div class="hotel-thumbnail" onclick="openImageModal('https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=1200')">
                        <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=300" alt="Hotel Room">
                    </div>
                    <div class="hotel-thumbnail" onclick="openImageModal('https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=1200')">
                        <img src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300" alt="Hotel Pool">
                    </div>
                    <div class="hotel-thumbnail" onclick="openImageModal('https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=1200')">
                        <img src="https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=300" alt="Hotel Restaurant">
                    </div>
                    <div class="hotel-thumbnail">See all photos</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="hotel-content">
            <div class="hotel-main-content">
                <!-- Highlights Section -->
                <div class="highlights-section">
                    <h3 class="highlights-title">
                        <span class="highlights-badge">Highlights</span>
                    </h3>
                    <p>Beautiful hotel with amazing amenities and great location.</p>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="hotel-sidebar">
                <!-- Booking Form -->
                <div class="booking-form-section">
                    <div class="booking-form-header">
                        <span class="from-label">FROM</span>
                        <div class="price-display" id="totalPrice">500.00 GEL</div>
                    </div>

                    <form class="booking-form" id="bookingForm">
                        <!-- Date Field -->
                        <div class="form-group">
                            <label for="booking-date">Date</label>
                            <input type="date" id="booking-date" name="booking-date" class="form-input" required>
                        </div>

                        <!-- Adults and Children Fields -->
                        <div class="hotel-form-row">
                            <div class="hotel-form-group">
                                <label for="adults">Adults</label>
                                <div class="hotel-quantity-selector">
                                    <button type="button" class="hotel-qty-btn hotel-minus" data-target="adults">-</button>
                                    <input type="number" id="adults" name="adults" value="2" min="1" max="4" class="hotel-qty-input" readonly>
                                    <button type="button" class="hotel-qty-btn hotel-plus" data-target="adults">+</button>
                                </div>
                                <small class="hotel-age-note">Age 18+</small>
                            </div>
                            
                            <div class="hotel-form-group">
                                <label for="children">Children</label>
                                <div class="hotel-quantity-selector">
                                    <button type="button" class="hotel-qty-btn hotel-minus" data-target="children">-</button>
                                    <input type="number" id="children" name="children" value="0" min="0" max="2" class="hotel-qty-input" readonly>
                                    <button type="button" class="hotel-qty-btn hotel-plus" data-target="children">+</button>
                                </div>
                                <small class="hotel-age-note">Age 6-17</small>
                            </div>
                        </div>

                        <!-- Guest Names -->
                        <div class="form-group">
                            <label>Guest names *</label>
                            <div class="guest-names" id="guestNames">
                                <!-- Dynamic content -->
                            </div>
                        </div>

                        <!-- Book Now Button -->
                        <button type="submit" class="book-now-btn">Book Now</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <div class="image-modal-content">
            <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" src="" alt="">
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const basePrice = 250;
                const adultsInput = document.getElementById('adults');
                const childrenInput = document.getElementById('children');
                const totalPriceDisplay = document.getElementById('totalPrice');
                const guestNamesContainer = document.getElementById('guestNames');
                
                if (adultsInput && childrenInput && totalPriceDisplay && guestNamesContainer) {
                    
                    function updatePrice() {
                        const adults = parseInt(adultsInput.value) || 2;
                        const children = parseInt(childrenInput.value) || 0;
                        const totalPrice = (adults * basePrice) + (children * basePrice * 0.5);
                        totalPriceDisplay.textContent = totalPrice.toFixed(2) + ' GEL';
                    }
                    
                    function updateGuestNames() {
                        const adults = parseInt(adultsInput.value) || 2;
                        const children = parseInt(childrenInput.value) || 0;
                        const totalGuests = adults + children;
                        
                        guestNamesContainer.innerHTML = '';
                        
                        for (let i = 0; i < totalGuests; i++) {
                            const guestRow = document.createElement('div');
                            guestRow.className = 'guest-name-row';
                            guestRow.innerHTML = `
                                <select class="title-select">
                                    <option>Mr</option>
                                    <option>Ms</option>
                                    <option>Mrs</option>
                                </select>
                                <input type="text" placeholder="Guest name" class="guest-input" required>
                            `;
                            guestNamesContainer.appendChild(guestRow);
                        }
                    }
                    
                    // Quantity buttons
                    document.addEventListener('click', function(e) {
                        if (e.target.classList.contains('hotel-qty-btn')) {
                            e.preventDefault();
                            
                            const target = e.target.getAttribute('data-target');
                            const input = document.getElementById(target);
                            const isPlus = e.target.classList.contains('hotel-plus');
                            
                            if (!input) return;
                            
                            const currentValue = parseInt(input.value) || 0;
                            const min = parseInt(input.getAttribute('min')) || 0;
                            const max = parseInt(input.getAttribute('max')) || 10;
                            
                            let newValue = currentValue;
                            if (isPlus && currentValue < max) {
                                newValue = currentValue + 1;
                            } else if (!isPlus && currentValue > min) {
                                newValue = currentValue - 1;
                            }
                            
                            // Check total people limit (4 max)
                            const adults = target === 'adults' ? newValue : parseInt(adultsInput.value) || 0;
                            const children = target === 'children' ? newValue : parseInt(childrenInput.value) || 0;
                            
                            if (adults + children <= 4) {
                                input.value = newValue;
                                updatePrice();
                                updateGuestNames();
                            }
                        }
                    });
                    
                    // Initialize
                    updatePrice();
                    updateGuestNames();
                    
                    // Form submission
                    document.getElementById('bookingForm').addEventListener('submit', function(e) {
                        e.preventDefault();
                        alert('Booking submitted! Total: ' + totalPriceDisplay.textContent);
                    });
                }
            }, 500);
        });

        // Image Modal Functions
        function openImageModal(imageSrc) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            
            if (modal && modalImage) {
                modalImage.src = imageSrc;
                modal.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            }
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }
    </script>
</body>
</html>
