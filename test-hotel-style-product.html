<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Style Product Page Test</title>
    <style>
        /* Include the hotel booking styles */
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }

        /* Hotel Booking Style Layout */
        .hotel-booking-layout {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .hotel-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5rem;
            gap: 2rem;
        }

        .hotel-title-section {
            flex: 1;
        }

        .hotel-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0 0 0.5rem 0;
            line-height: 1.2;
        }

        .hotel-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .rating-stars {
            display: flex;
            gap: 2px;
        }

        .star {
            width: 16px;
            height: 16px;
            color: #fbbf24;
        }

        .hotel-address {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .hotel-address a {
            color: #3b82f6;
            text-decoration: none;
        }

        .price-match-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            color: #059669;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .select-rooms-btn,
        .price-display-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
        }

        .select-rooms-btn:hover,
        .price-display-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .price-display-btn .price-amount {
            color: white;
            font-size: 1.125rem;
            font-weight: 700;
        }

        /* Booking Form Styles */
        .booking-form-section {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            max-width: 320px;
        }

        .booking-form-header {
            text-align: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .from-label {
            display: block;
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .price-display {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }

        .booking-form {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group-row {
            display: flex;
            gap: 0.75rem;
        }

        .form-group-row .form-group {
            flex: 1;
        }

        .form-group label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .quantity-selector {
            display: flex;
            align-items: center;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            overflow: hidden;
        }

        .qty-btn {
            background: #f9fafb;
            border: none;
            padding: 0.75rem;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            transition: background-color 0.2s ease;
            min-width: 40px;
        }

        .qty-btn:hover {
            background: #e5e7eb;
        }

        .qty-input {
            border: none;
            text-align: center;
            font-weight: 600;
            padding: 0.75rem 0.5rem;
            min-width: 60px;
            background: white;
        }

        .age-note {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .guest-names {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .guest-name-row {
            display: flex;
            gap: 0.5rem;
        }

        .title-select {
            flex: 0 0 80px;
            padding: 0.75rem 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            background: white;
        }

        .guest-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .book-now-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 1rem;
        }

        .book-now-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* Hotel Gallery Grid */
        .hotel-gallery-container {
            margin-bottom: 2rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .hotel-gallery-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4px;
            height: 400px;
        }

        .hotel-main-image {
            position: relative;
            overflow: hidden;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .hotel-thumbnails {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 4px;
        }

        .hotel-thumbnail {
            position: relative;
            overflow: hidden;
            cursor: pointer;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 0.75rem;
        }

        /* Navigation Tabs */
        .hotel-nav-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 2rem;
            overflow-x: auto;
        }

        .hotel-nav-tab {
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            color: #6b7280;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            white-space: nowrap;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .hotel-nav-tab:hover {
            color: #374151;
        }

        .hotel-nav-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        /* Content Sections */
        .hotel-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
            align-items: start;
        }

        .hotel-main-content {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .hotel-sidebar {
            position: sticky;
            top: 2rem;
        }

        /* Sections */
        .highlights-section,
        .amenities-section,
        .rating-section,
        .surroundings-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .highlights-title,
        .amenities-title,
        .surroundings-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .highlights-grid,
        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .highlight-item,
        .amenity-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 8px;
            background: #f9fafb;
            transition: background-color 0.2s ease;
        }

        .highlight-item:hover,
        .amenity-item:hover {
            background: #f3f4f6;
        }

        .highlight-icon,
        .amenity-icon {
            width: 20px;
            height: 20px;
            color: #3b82f6;
            flex-shrink: 0;
        }

        .amenity-icon {
            color: #6b7280;
        }

        .highlight-text,
        .amenity-text {
            font-size: 0.875rem;
            color: #374151;
            font-weight: 500;
        }

        .amenity-text {
            font-weight: 400;
        }

        /* Rating Section */
        .rating-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .rating-score {
            background: #3b82f6;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            font-weight: 700;
            font-size: 1.125rem;
        }

        .rating-label {
            color: #3b82f6;
            font-weight: 600;
            font-size: 1rem;
        }

        .rating-description {
            color: #6b7280;
            font-size: 0.875rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .rating-reviews-link,
        .amenities-link {
            color: #3b82f6;
            font-size: 0.875rem;
            text-decoration: none;
            font-weight: 500;
        }

        .amenities-link {
            margin-top: 1rem;
            display: inline-block;
        }

        /* Surroundings */
        .surroundings-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .surrounding-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
        }

        .surrounding-icon {
            width: 16px;
            height: 16px;
            color: #6b7280;
            flex-shrink: 0;
        }

        .surrounding-text {
            font-size: 0.875rem;
            color: #374151;
            flex: 1;
        }

        .surrounding-distance {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* Mobile Responsive */
        @media screen and (max-width: 768px) {
            .hotel-booking-layout {
                padding: 1rem;
            }
            
            .hotel-header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .hotel-title {
                font-size: 1.5rem;
            }
            
            .hotel-gallery-grid {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .hotel-main-image {
                height: 250px;
            }
            
            .hotel-thumbnails {
                grid-template-columns: repeat(4, 1fr);
                grid-template-rows: 1fr;
                height: 80px;
            }
            
            .hotel-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .highlights-grid,
            .amenities-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="hotel-booking-layout">
        <!-- Hotel Header -->
        <div class="hotel-header">
            <div class="hotel-title-section">
                <h1 class="hotel-title">Metropole Bangkok</h1>
                
                <div class="hotel-rating">
                    <div class="rating-stars">
                        <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                    </div>
                </div>
                
                <div class="hotel-address">
                    © 2025 New Petchaburi Rd, Huai Khwang, Bangkok, 10310, Thailand
                    <a href="#location">Show on map</a>
                </div>
                
                <div class="price-match-badge">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    We Price Match
                </div>
            </div>
            
            <div class="price-display-btn">
                <span class="price-amount">$299.00</span>
            </div>
        </div>

        <!-- Hotel Gallery -->
        <div class="hotel-gallery-container">
            <div class="hotel-gallery-grid">
                <div class="hotel-main-image">
                    Main Hotel Image
                </div>
                
                <div class="hotel-thumbnails">
                    <div class="hotel-thumbnail">Image 1</div>
                    <div class="hotel-thumbnail">Image 2</div>
                    <div class="hotel-thumbnail">Image 3</div>
                    <div class="hotel-thumbnail">See all photos</div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="hotel-nav-tabs">
            <button class="hotel-nav-tab active">Overview</button>
            <button class="hotel-nav-tab">Rooms</button>
            <button class="hotel-nav-tab">Guest Reviews</button>
            <button class="hotel-nav-tab">Services & Amenities</button>
            <button class="hotel-nav-tab">Policies</button>
            <button class="hotel-nav-tab">Location</button>
        </div>

        <!-- Main Content -->
        <div class="hotel-content">
            <div class="hotel-main-content">
                <!-- Highlights Section -->
                <div class="highlights-section">
                    <h3 class="highlights-title">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
                        </svg>
                        Highlights
                    </h3>
                    
                    <div class="highlights-grid">
                        <div class="highlight-item">
                            <svg class="highlight-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm12 12H4V8h12v8z" clip-rule="evenodd"/>
                            </svg>
                            <span class="highlight-text">Low-rise building</span>
                        </div>
                        
                        <div class="highlight-item">
                            <svg class="highlight-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                                <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
                            </svg>
                            <span class="highlight-text">Free parking</span>
                        </div>
                        
                        <div class="highlight-item">
                            <svg class="highlight-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z" clip-rule="evenodd"/>
                            </svg>
                            <span class="highlight-text">Delicious breakfast</span>
                        </div>
                        
                        <div class="highlight-item">
                            <svg class="highlight-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"/>
                            </svg>
                            <span class="highlight-text">Lots to do</span>
                        </div>
                    </div>
                </div>

                <!-- Amenities Section -->
                <div class="amenities-section">
                    <h3 class="amenities-title">Amenities</h3>
                    
                    <div class="amenities-grid">
                        <div class="amenity-item">
                            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                            </svg>
                            <span class="amenity-text">Outdoor swimming pool</span>
                        </div>
                        
                        <div class="amenity-item">
                            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                            <span class="amenity-text">Sauna</span>
                        </div>
                        
                        <div class="amenity-item">
                            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z" clip-rule="evenodd"/>
                            </svg>
                            <span class="amenity-text">Gym</span>
                        </div>
                        
                        <div class="amenity-item">
                            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"/>
                            </svg>
                            <span class="amenity-text">Bar</span>
                        </div>
                        
                        <div class="amenity-item">
                            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z" clip-rule="evenodd"/>
                            </svg>
                            <span class="amenity-text">Restaurant</span>
                        </div>
                        
                        <div class="amenity-item">
                            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                                <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
                            </svg>
                            <span class="amenity-text">Public parking <span style="color: #059669; font-weight: 500;">Free</span></span>
                        </div>
                        
                        <div class="amenity-item">
                            <svg class="amenity-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm12 12H4V8h12v8z" clip-rule="evenodd"/>
                            </svg>
                            <span class="amenity-text">Conference room</span>
                        </div>
                    </div>
                    
                    <a href="#amenities" class="amenities-link">All amenities</a>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="hotel-sidebar">
                <!-- Booking Form -->
                <div class="booking-form-section">
                    <div class="booking-form-header">
                        <span class="from-label">FROM</span>
                        <div class="price-display" id="totalPrice">500.00 GEL</div>
                    </div>

                    <form class="booking-form" id="bookingForm">
                        <!-- Date Field -->
                        <div class="form-group">
                            <label for="booking-date">Date</label>
                            <input type="date" id="booking-date" name="booking-date" class="form-input" required>
                        </div>

                        <!-- Adults and Children Fields -->
                        <div class="form-group-row">
                            <div class="form-group">
                                <label for="adults">Adults</label>
                                <div class="quantity-selector">
                                    <button type="button" class="qty-btn minus" data-target="adults">-</button>
                                    <input type="number" id="adults" name="adults" value="2" min="1" max="4" class="qty-input" readonly>
                                    <button type="button" class="qty-btn plus" data-target="adults">+</button>
                                </div>
                                <small class="age-note">Age 18+</small>
                            </div>

                            <div class="form-group">
                                <label for="children">Children</label>
                                <div class="quantity-selector">
                                    <button type="button" class="qty-btn minus" data-target="children">-</button>
                                    <input type="number" id="children" name="children" value="0" min="0" max="2" class="qty-input" readonly>
                                    <button type="button" class="qty-btn plus" data-target="children">+</button>
                                </div>
                                <small class="age-note">Age 6-17</small>
                            </div>
                        </div>

                        <!-- Guest Names -->
                        <div class="form-group">
                            <label>Guest names *</label>
                            <div class="guest-names" id="guestNames">
                                <div class="guest-name-row">
                                    <select class="title-select">
                                        <option>Mr</option>
                                        <option>Ms</option>
                                        <option>Mrs</option>
                                    </select>
                                    <input type="text" placeholder="Guest name" class="guest-input" required>
                                </div>
                                <div class="guest-name-row">
                                    <select class="title-select">
                                        <option>Mr</option>
                                        <option>Ms</option>
                                        <option>Mrs</option>
                                    </select>
                                    <input type="text" placeholder="Guest name" class="guest-input" required>
                                </div>
                            </div>
                        </div>

                        <!-- Book Now Button -->
                        <button type="submit" class="book-now-btn">Book Now</button>
                    </form>
                </div>

                <!-- Surroundings Section -->
                <div class="surroundings-section">
                    <h3 class="surroundings-title">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                        </svg>
                        Surroundings
                    </h3>
                    
                    <div class="surroundings-list">
                        <div class="surrounding-item">
                            <svg class="surrounding-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                            </svg>
                            <span class="surrounding-text">Metro: Ramkhamhaeng</span>
                            <span class="surrounding-distance">(1.9 km)</span>
                        </div>
                        
                        <div class="surrounding-item">
                            <svg class="surrounding-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                            </svg>
                            <span class="surrounding-text">Metro: Thong Lo</span>
                            <span class="surrounding-distance">(2.8 km)</span>
                        </div>
                        
                        <div class="surrounding-item">
                            <svg class="surrounding-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 2L3 7v11a2 2 0 002 2h4v-6h2v6h4a2 2 0 002-2V7l-7-5z"/>
                            </svg>
                            <span class="surrounding-text">Airport: Suvarnabhumi Airport</span>
                            <span class="surrounding-distance">(24.9 km)</span>
                        </div>
                        
                        <div class="surrounding-item">
                            <svg class="surrounding-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 2L3 7v11a2 2 0 002 2h4v-6h2v6h4a2 2 0 002-2V7l-7-5z"/>
                            </svg>
                            <span class="surrounding-text">Airport: Don Mueang International Airport</span>
                            <span class="surrounding-distance">(25.0 km)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.hotel-nav-tab');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Booking form functionality
            const basePrice = 250; // Base price per adult
            const adultsInput = document.getElementById('adults');
            const childrenInput = document.getElementById('children');
            const totalPriceDisplay = document.getElementById('totalPrice');
            const guestNamesContainer = document.getElementById('guestNames');

            // Quantity selector functionality
            document.querySelectorAll('.qty-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = this.getAttribute('data-target');
                    const input = document.getElementById(target);

                    if (!input) return;

                    const isPlus = this.classList.contains('plus');
                    const currentValue = parseInt(input.value) || 0;
                    const min = parseInt(input.getAttribute('min')) || 0;
                    const max = parseInt(input.getAttribute('max')) || 10;

                    let newValue = currentValue;
                    if (isPlus && currentValue < max) {
                        newValue = currentValue + 1;
                    } else if (!isPlus && currentValue > min) {
                        newValue = currentValue - 1;
                    }

                    // Check total people limit (4 max)
                    const adults = target === 'adults' ? newValue : parseInt(adultsInput.value) || 0;
                    const children = target === 'children' ? newValue : parseInt(childrenInput.value) || 0;

                    if (adults + children <= 4) {
                        input.value = newValue;
                        updatePrice();
                        updateGuestNames();
                    }
                });
            });

            function updatePrice() {
                const adults = parseInt(adultsInput.value);
                const children = parseInt(childrenInput.value);
                const totalPrice = (adults * basePrice) + (children * basePrice * 0.5); // Children 50% price
                totalPriceDisplay.textContent = totalPrice.toFixed(2) + ' GEL';
            }

            function updateGuestNames() {
                const adults = parseInt(adultsInput.value);
                const children = parseInt(childrenInput.value);
                const totalGuests = adults + children;

                guestNamesContainer.innerHTML = '';

                for (let i = 0; i < totalGuests; i++) {
                    const guestRow = document.createElement('div');
                    guestRow.className = 'guest-name-row';
                    guestRow.innerHTML = `
                        <select class="title-select">
                            <option>Mr</option>
                            <option>Ms</option>
                            <option>Mrs</option>
                        </select>
                        <input type="text" placeholder="Guest name" class="guest-input" required>
                    `;
                    guestNamesContainer.appendChild(guestRow);
                }
            }

            // Initialize
            updatePrice();
            updateGuestNames();

            // Form submission
            document.getElementById('bookingForm').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Booking submitted! Total: ' + totalPriceDisplay.textContent);
            });
        });
    </script>
</body>
</html>
