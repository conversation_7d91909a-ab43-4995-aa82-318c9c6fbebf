<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elegant Image Modal Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }

        .hotel-gallery-container {
            max-width: 800px;
            margin: 0 auto;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .hotel-gallery-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4px;
            height: 400px;
        }

        .hotel-main-image {
            position: relative;
            overflow: hidden;
            cursor: pointer;
            background: #e5e7eb;
        }

        .hotel-main-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .hotel-main-image:hover img {
            transform: scale(1.05);
        }

        .hotel-thumbnails {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 4px;
        }

        .hotel-thumbnail {
            position: relative;
            overflow: hidden;
            cursor: pointer;
            background: #e5e7eb;
        }

        .hotel-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .hotel-thumbnail:hover img {
            transform: scale(1.1);
        }

        .image-zoom-icon {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            padding: 0.75rem;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
        }

        .hotel-main-image:hover .image-zoom-icon,
        .hotel-thumbnail:hover .image-zoom-icon {
            opacity: 1;
            transform: scale(1.1);
            background: rgba(0, 0, 0, 0.8);
        }

        .hotel-main-image,
        .hotel-thumbnail {
            cursor: pointer;
            position: relative;
        }

        /* View All Photos Overlay */
        .view-all-overlay {
            position: relative;
        }

        .view-all-photos {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .view-all-photos:hover {
            background: rgba(0, 0, 0, 0.8);
        }

        .view-all-text {
            text-align: center;
            font-size: 0.9rem;
            line-height: 1.3;
        }

        .view-all-label {
            display: block;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .view-all-count {
            display: block;
            font-size: 0.8rem;
            opacity: 0.9;
        }

        /* Enhanced Image Modal Styles */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.95);
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(10px);
        }

        .image-modal-content {
            position: relative;
            max-width: 95%;
            max-height: 95%;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .modal-counter {
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .image-modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
        }

        .image-modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.1);
        }

        .modal-image-container {
            position: relative;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }

        .modal-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .modal-nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.5);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
        }

        .modal-nav-btn:hover {
            background: rgba(0, 0, 0, 0.7);
            transform: translateY(-50%) scale(1.1);
        }

        .modal-prev {
            left: 1rem;
        }

        .modal-next {
            right: 1rem;
        }

        .modal-thumbnails {
            display: flex;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            overflow-x: auto;
        }

        .modal-thumb {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .modal-thumb:hover {
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        .modal-thumb.active {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }

        .modal-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; margin-bottom: 2rem;">Elegant Image Modal Test</h1>
    
    <div class="hotel-gallery-container">
        <div class="hotel-gallery-grid">
            <div class="hotel-main-image" onclick="openImageModal('https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1200', 0)">
                <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800" alt="Hotel Main Image">
                <div class="image-zoom-icon">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="m21 21-4.35-4.35"/>
                        <circle cx="11" cy="11" r="3"/>
                    </svg>
                </div>
            </div>
            
            <div class="hotel-thumbnails">
                <div class="hotel-thumbnail" onclick="openImageModal('https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=1200', 1)">
                    <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=300" alt="Hotel Room">
                </div>
                <div class="hotel-thumbnail" onclick="openImageModal('https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=1200', 2)">
                    <img src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300" alt="Hotel Pool">
                </div>
                <div class="hotel-thumbnail" onclick="openImageModal('https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=1200', 3)">
                    <img src="https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=300" alt="Hotel Restaurant">
                </div>
                <div class="hotel-thumbnail view-all-overlay" onclick="openImageModal('https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=1200', 4)">
                    <img src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=300" alt="Hotel Lobby">
                    <div class="view-all-photos">
                        <div class="view-all-text">
                            <span class="view-all-label">View all</span>
                            <span class="view-all-count">8 Images</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Image Modal -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal(event)">
        <div class="image-modal-content" onclick="event.stopPropagation()">
            <div class="modal-header">
                <div class="modal-counter">
                    <span id="currentImageIndex">1</span> / <span id="totalImages">5</span>
                </div>
                <button class="image-modal-close" onclick="closeImageModal()">&times;</button>
            </div>
            
            <div class="modal-image-container">
                <button class="modal-nav-btn modal-prev" onclick="previousImage()" id="prevBtn">
                    <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                
                <img id="modalImage" src="" alt="" class="modal-image">
                
                <button class="modal-nav-btn modal-next" onclick="nextImage()" id="nextBtn">
                    <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
            
            <div class="modal-thumbnails" id="modalThumbnails">
                <!-- Dynamic thumbnails will be added here -->
            </div>
        </div>
    </div>

    <script>
        // Enhanced Image Modal Functions
        let currentImageIndex = 0;
        let imageGallery = [
            {
                src: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1200',
                thumb: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=300',
                alt: 'Hotel Main Image'
            },
            {
                src: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=1200',
                thumb: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=300',
                alt: 'Hotel Room'
            },
            {
                src: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=1200',
                thumb: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300',
                alt: 'Hotel Pool'
            },
            {
                src: 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=1200',
                thumb: 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=300',
                alt: 'Hotel Restaurant'
            },
            {
                src: 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=1200',
                thumb: 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=300',
                alt: 'Hotel Lobby'
            },
            {
                src: 'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=1200',
                thumb: 'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=300',
                alt: 'Hotel Spa'
            },
            {
                src: 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=1200',
                thumb: 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=300',
                alt: 'Hotel Gym'
            },
            {
                src: 'https://images.unsplash.com/photo-1584132967334-10e028bd69f7?w=1200',
                thumb: 'https://images.unsplash.com/photo-1584132967334-10e028bd69f7?w=300',
                alt: 'Hotel Balcony'
            }
        ];

        function openImageModal(imageSrc, imageIndex = 0) {
            currentImageIndex = imageIndex;
            
            const modal = document.getElementById('imageModal');
            if (modal) {
                updateModalImage();
                createThumbnails();
                modal.style.display = 'flex';
                document.body.style.overflow = 'hidden';
                
                // Add keyboard navigation
                document.addEventListener('keydown', handleKeyNavigation);
            }
        }

        function closeImageModal(event) {
            if (event && event.target !== event.currentTarget) return;
            
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
                document.removeEventListener('keydown', handleKeyNavigation);
            }
        }

        function updateModalImage() {
            const modalImage = document.getElementById('modalImage');
            const currentIndexSpan = document.getElementById('currentImageIndex');
            
            if (modalImage && imageGallery[currentImageIndex]) {
                modalImage.src = imageGallery[currentImageIndex].src;
                modalImage.alt = imageGallery[currentImageIndex].alt;
                
                if (currentIndexSpan) {
                    currentIndexSpan.textContent = currentImageIndex + 1;
                }
                
                updateNavigationButtons();
                updateActiveThumbnail();
            }
        }

        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            if (prevBtn) {
                prevBtn.style.display = currentImageIndex > 0 ? 'flex' : 'none';
            }
            
            if (nextBtn) {
                nextBtn.style.display = currentImageIndex < imageGallery.length - 1 ? 'flex' : 'none';
            }
        }

        function createThumbnails() {
            const thumbnailsContainer = document.getElementById('modalThumbnails');
            if (!thumbnailsContainer) return;
            
            thumbnailsContainer.innerHTML = '';
            
            imageGallery.forEach((image, index) => {
                const thumbDiv = document.createElement('div');
                thumbDiv.className = `modal-thumb ${index === currentImageIndex ? 'active' : ''}`;
                thumbDiv.onclick = () => goToImage(index);
                
                const thumbImg = document.createElement('img');
                thumbImg.src = image.thumb;
                thumbImg.alt = image.alt;
                
                thumbDiv.appendChild(thumbImg);
                thumbnailsContainer.appendChild(thumbDiv);
            });
        }

        function updateActiveThumbnail() {
            const thumbs = document.querySelectorAll('.modal-thumb');
            thumbs.forEach((thumb, index) => {
                thumb.classList.toggle('active', index === currentImageIndex);
            });
        }

        function previousImage() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateModalImage();
            }
        }

        function nextImage() {
            if (currentImageIndex < imageGallery.length - 1) {
                currentImageIndex++;
                updateModalImage();
            }
        }

        function goToImage(index) {
            currentImageIndex = index;
            updateModalImage();
        }

        function handleKeyNavigation(event) {
            switch(event.key) {
                case 'Escape':
                    closeImageModal();
                    break;
                case 'ArrowLeft':
                    previousImage();
                    break;
                case 'ArrowRight':
                    nextImage();
                    break;
            }
        }
    </script>
</body>
</html>
