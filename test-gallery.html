<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Gallery Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 2rem;
        }
        
        .page-width {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: #333;
        }
        
        /* Include our gallery CSS */
        .hotel-gallery-section {
            margin: 0;
        }

        .hotel-gallery-container {
            margin-top: 2rem;
        }

        .hotel-gallery-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4px;
            height: 400px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        @media screen and (max-width: 749px) {
            .hotel-gallery-grid {
                grid-template-columns: 1fr;
                height: auto;
            }
        }

        .hotel-gallery-main {
            position: relative;
            overflow: hidden;
        }

        .hotel-gallery-main-image {
            position: relative;
            width: 100%;
            height: 100%;
            cursor: pointer;
            overflow: hidden;
        }

        .hotel-gallery-main-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .hotel-gallery-main-image:hover img {
            transform: scale(1.05);
        }

        .hotel-gallery-overlay {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            z-index: 2;
        }

        .hotel-gallery-view-all {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.75rem 1.25rem;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .hotel-gallery-view-all:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .hotel-gallery-icon {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hotel-gallery-icon svg {
            width: 16px;
            height: 16px;
        }

        .hotel-gallery-thumbnails {
            display: grid;
            grid-template-rows: repeat(2, 1fr);
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
        }

        @media screen and (max-width: 749px) {
            .hotel-gallery-thumbnails {
                grid-template-rows: 1fr;
                grid-template-columns: repeat(4, 1fr);
                height: 100px;
                margin-top: 0.5rem;
            }
        }

        .hotel-gallery-thumb {
            position: relative;
            overflow: hidden;
            cursor: pointer;
            border-radius: 4px;
        }

        .hotel-gallery-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .hotel-gallery-thumb:hover img {
            transform: scale(1.1);
        }

        .hotel-gallery-more-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        /* Modal Styles */
        .hotel-gallery-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .hotel-gallery-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .hotel-gallery-modal-content {
            width: 90%;
            max-width: 1200px;
            height: 90%;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .hotel-gallery-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #eee;
        }

        .hotel-gallery-modal-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .hotel-gallery-modal-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            font-size: 1.5rem;
        }

        .hotel-gallery-modal-close:hover {
            background: #f5f5f5;
        }

        .hotel-gallery-modal-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .hotel-gallery-modal-main {
            flex: 1;
            display: flex;
            align-items: center;
            position: relative;
            background: #f8f9fa;
        }

        .hotel-gallery-modal-image-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .hotel-gallery-modal-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .hotel-gallery-modal-prev,
        .hotel-gallery-modal-next {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            z-index: 10;
            font-size: 1.5rem;
        }

        .hotel-gallery-modal-prev {
            left: 1rem;
        }

        .hotel-gallery-modal-next {
            right: 1rem;
        }

        .hotel-gallery-modal-prev:hover,
        .hotel-gallery-modal-next:hover {
            background: white;
            transform: translateY(-50%) scale(1.1);
        }

        .hotel-gallery-modal-thumbnails {
            display: flex;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            overflow-x: auto;
            border-top: 1px solid #eee;
            background: white;
        }

        .hotel-gallery-modal-thumb {
            flex-shrink: 0;
            width: 80px;
            height: 60px;
            border-radius: 6px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.2s ease;
        }

        .hotel-gallery-modal-thumb.active {
            border-color: #007bff;
        }

        .hotel-gallery-modal-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        hotel-gallery {
            display: block;
        }
    </style>
</head>
<body>
    <div class="page-width">
        <h1 class="title">Hotel Style Gallery Demo</h1>
        
        <hotel-gallery class="hotel-gallery-section">
            <div class="hotel-gallery-container">
                <div class="hotel-gallery-grid">
                    <div class="hotel-gallery-main">
                        <div class="hotel-gallery-main-image" data-gallery-item="0">
                            <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop" alt="Hotel exterior" loading="lazy">
                            <div class="hotel-gallery-overlay">
                                <button class="hotel-gallery-view-all" type="button">
                                    <span class="hotel-gallery-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                            <circle cx="8.5" cy="8.5" r="1.5"/>
                                            <polyline points="21,15 16,10 5,21"/>
                                        </svg>
                                    </span>
                                    <span>See All 6 Photos</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="hotel-gallery-thumbnails">
                        <div class="hotel-gallery-thumb" data-gallery-item="1">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=300&h=200&fit=crop" alt="Hotel lobby">
                        </div>
                        <div class="hotel-gallery-thumb" data-gallery-item="2">
                            <img src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300&h=200&fit=crop" alt="Hotel room">
                        </div>
                        <div class="hotel-gallery-thumb" data-gallery-item="3">
                            <img src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=300&h=200&fit=crop" alt="Hotel restaurant">
                        </div>
                        <div class="hotel-gallery-thumb" data-gallery-item="4">
                            <img src="https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=300&h=200&fit=crop" alt="Hotel pool">
                            <div class="hotel-gallery-more-overlay">
                                <span>+2</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal -->
            <div class="hotel-gallery-modal">
                <div class="hotel-gallery-modal-content">
                    <div class="hotel-gallery-modal-header">
                        <h3>Gallery</h3>
                        <button class="hotel-gallery-modal-close" type="button">×</button>
                    </div>
                    <div class="hotel-gallery-modal-body">
                        <div class="hotel-gallery-modal-main">
                            <button class="hotel-gallery-modal-prev" type="button">‹</button>
                            <div class="hotel-gallery-modal-image-container">
                                <img class="hotel-gallery-modal-image" src="" alt="">
                            </div>
                            <button class="hotel-gallery-modal-next" type="button">›</button>
                        </div>
                        <div class="hotel-gallery-modal-thumbnails">
                            <div class="hotel-gallery-modal-thumb" data-index="0">
                                <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=100&h=75&fit=crop" alt="Hotel exterior" data-full-src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1200&h=800&fit=crop">
                            </div>
                            <div class="hotel-gallery-modal-thumb" data-index="1">
                                <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=100&h=75&fit=crop" alt="Hotel lobby" data-full-src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=1200&h=800&fit=crop">
                            </div>
                            <div class="hotel-gallery-modal-thumb" data-index="2">
                                <img src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=100&h=75&fit=crop" alt="Hotel room" data-full-src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=1200&h=800&fit=crop">
                            </div>
                            <div class="hotel-gallery-modal-thumb" data-index="3">
                                <img src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=100&h=75&fit=crop" alt="Hotel restaurant" data-full-src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=1200&h=800&fit=crop">
                            </div>
                            <div class="hotel-gallery-modal-thumb" data-index="4">
                                <img src="https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=100&h=75&fit=crop" alt="Hotel pool" data-full-src="https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=1200&h=800&fit=crop">
                            </div>
                            <div class="hotel-gallery-modal-thumb" data-index="5">
                                <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?w=100&h=75&fit=crop" alt="Hotel spa" data-full-src="https://images.unsplash.com/photo-1590490360182-c33d57733427?w=1200&h=800&fit=crop">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </hotel-gallery>
    </div>

    <script>
        class HotelGallery extends HTMLElement {
            constructor() {
                super();
                this.modal = this.querySelector('.hotel-gallery-modal');
                this.modalImage = this.querySelector('.hotel-gallery-modal-image');
                this.modalThumbs = this.querySelectorAll('.hotel-gallery-modal-thumb');
                this.currentIndex = 0;
                this.images = [];
                
                // Collect all images
                this.modalThumbs.forEach((thumb, index) => {
                    const img = thumb.querySelector('img');
                    this.images.push({
                        src: img.dataset.fullSrc,
                        alt: img.alt
                    });
                });
                
                this.bindEvents();
            }
            
            bindEvents() {
                // Open modal
                this.querySelector('.hotel-gallery-view-all')?.addEventListener('click', () => {
                    this.openModal(0);
                });
                
                // Gallery items click
                this.querySelectorAll('[data-gallery-item]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const index = parseInt(e.currentTarget.dataset.galleryItem);
                        this.openModal(index);
                    });
                });
                
                // Modal controls
                this.querySelector('.hotel-gallery-modal-close')?.addEventListener('click', () => {
                    this.closeModal();
                });
                
                this.querySelector('.hotel-gallery-modal-prev')?.addEventListener('click', () => {
                    this.prevImage();
                });
                
                this.querySelector('.hotel-gallery-modal-next')?.addEventListener('click', () => {
                    this.nextImage();
                });
                
                // Thumbnail clicks
                this.modalThumbs.forEach((thumb, index) => {
                    thumb.addEventListener('click', () => {
                        this.showImage(index);
                    });
                });
                
                // Close on backdrop click
                this.modal?.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        this.closeModal();
                    }
                });
                
                // Keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (!this.modal.classList.contains('active')) return;
                    
                    switch(e.key) {
                        case 'Escape':
                            this.closeModal();
                            break;
                        case 'ArrowLeft':
                            this.prevImage();
                            break;
                        case 'ArrowRight':
                            this.nextImage();
                            break;
                    }
                });
            }
            
            openModal(index = 0) {
                this.currentIndex = index;
                this.showImage(index);
                this.modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
            
            closeModal() {
                this.modal.classList.remove('active');
                document.body.style.overflow = '';
            }
            
            showImage(index) {
                if (index < 0 || index >= this.images.length) return;
                
                this.currentIndex = index;
                this.modalImage.src = this.images[index].src;
                this.modalImage.alt = this.images[index].alt;
                
                // Update active thumbnail
                this.modalThumbs.forEach((thumb, i) => {
                    thumb.classList.toggle('active', i === index);
                });
            }
            
            nextImage() {
                const nextIndex = (this.currentIndex + 1) % this.images.length;
                this.showImage(nextIndex);
            }
            
            prevImage() {
                const prevIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
                this.showImage(prevIndex);
            }
        }
        
        customElements.define('hotel-gallery', HotelGallery);
    </script>
</body>
</html>
