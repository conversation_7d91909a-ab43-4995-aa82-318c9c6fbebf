{
  "general": {
    "password_page": {
      "login_form_heading": "შეიყვანეთ მაღაზიის პაროლი:",
      "login_password_button": "შესვლა პაროლით",
      "login_form_password_label": "პაროლი",
      "login_form_password_placeholder": "თქვენი პაროლი",
      "login_form_error": "არასწორი პაროლი!",
      "login_form_submit": "შესვლა",
      "admin_link_html": "ხართ მაღაზიის მფლობელი? <a href=\"/admin\" class=\"link underlined-link\">შედით აქ</a>",
      "powered_by_shopify_html": "ეს მაღაზია იმუშავებს {{ shopify }}-ზე"
    },
    "social": {
      "alt_text": {
        "share_on_facebook": "გაზიარება Facebook-ზე",
        "share_on_twitter": "გაზიარება X-ზე",
        "share_on_pinterest": "დაპინვა Pinterest-ზე"
      },
      "links": {
        "twitter": "X (Twitter)",
        "facebook": "Facebook",
        "pinterest": "Pinterest",
        "instagram": "Instagram",
        "tumblr": "Tumblr",
        "snapchat": "Snapchat",
        "youtube": "YouTube",
        "vimeo": "Vimeo",
        "tiktok": "TikTok"
      }
    },
    "continue_shopping": "შოპინგის გაგრძელება",
    "pagination": {
      "label": "გვერდების ნუმერაცია",
      "page": "გვერდი {{ number }}",
      "next": "შემდეგი გვერდი",
      "previous": "წინა გვერდი"
    },
    "search": {
      "search": "ძიება",
      "reset": "საძიებო ტერმინის გასუფთავება"
    },
    "cart": {
      "view": "კალათის ნახვა ({{ count }})",
      "view_empty_cart": "კალათის ნახვა",
      "item_added": "ნივთი დაემატა კალათაში"
    },
    "share": {
      "close": "გაზიარების დახურვა",
      "copy_to_clipboard": "ლინკის კოპირება",
      "share_url": "ლინკი",
      "success_message": "ლინკი დაკოპირდა"
    },
    "slider": {
      "of": "-დან",
      "next_slide": "მარჯვნივ გადაადგილება",
      "previous_slide": "მარცხნივ გადაადგილება",
      "name": "სლაიდერი"
    }
  },
  "newsletter": {
    "label": "ელ-ფოსტა",
    "success": "გმადლობთ გამოწერისთვის",
    "button_label": "გამოწერა"
  },
  "accessibility": {
    "skip_to_text": "შინაარსზე გადასვლა",
    "skip_to_product_info": "პროდუქტის ინფორმაციაზე გადასვლა",
    "close": "დახურვა",
    "unit_price_separator": "თითო",
    "vendor": "მომწოდებელი:",
    "error": "შეცდომა",
    "refresh_page": "არჩევანის გაკეთება იწვევს გვერდის სრულ განახლებას.",
    "link_messages": {
      "new_window": "იხსნება ახალ ფანჯარაში.",
      "external": "იხსნება გარე ვებსაიტი."
    },
    "loading": "იტვირთება...",
    "total_reviews": "სულ მიმოხილვები",
    "star_reviews_info": "{{ rating_value }} {{ rating_max }}-დან ვარსკვლავი",
    "collapsible_content_title": "ჩამოსაშლელი შინაარსი",
    "complementary_products": "დამატებითი პროდუქტები"
  },
  "templates": {
    "search": {
      "no_results": "შედეგები ვერ მოიძებნა "{{ terms }}"-სთვის. შეამოწმეთ მართლწერა ან გამოიყენეთ სხვა სიტყვა ან ფრაზა.",
      "page": "გვერდი",
      "products": "პროდუქტები",
      "results_pages_with_count": {
        "one": "{{ count }} გვერდი",
        "other": "{{ count }} გვერდი"
      },
      "results_suggestions_with_count": {
        "one": "{{ count }} წინადადება",
        "other": "{{ count }} წინადადება"
      },
      "results_products_with_count": {
        "one": "{{ count }} პროდუქტი",
        "other": "{{ count }} პროდუქტი"
      },
      "results_with_count": {
        "one": "{{ count }} შედეგი",
        "other": "{{ count }} შედეგი"
      },
      "results_with_count_and_term": {
        "one": "{{ count }} შედეგი მოიძებნა "{{ terms }}"-სთვის",
        "other": "{{ count }} შედეგი მოიძებნა "{{ terms }}"-სთვის"
      },
      "title": "ძიების შედეგები",
      "search_for": "ძიება "{{ terms }}"-სთვის",
      "suggestions": "წინადადებები",
      "pages": "გვერდები"
    },
    "cart": {
      "cart": "კალათა"
    },
    "contact": {
      "form": {
        "title": "საკონტაქტო ფორმა",
        "name": "სახელი",
        "email": "ელ-ფოსტა",
        "phone": "ტელეფონის ნომერი",
        "comment": "კომენტარი",
        "send": "გაგზავნა",
        "post_success": "გმადლობთ ჩვენთან დაკავშირებისთვის. ჩვენ მალე დაგიკავშირდებით.",
        "error_heading": "გთხოვთ შეასწოროთ შემდეგი:"
      }
    },
    "404": {
      "title": "გვერდი ვერ მოიძებნა",
      "subtext": "404"
    }
  },
  "products": {
    "booking": {
      "from": "დან",
      "date": "თარიღი",
      "adults": "ზრდასრულები",
      "children": "ბავშვები",
      "infants": "ჩვილები",
      "age_18_plus": "ასაკი 18+",
      "age_6_17": "ასაკი 6-17",
      "age_0_5": "ასაკი 0-5",
      "guest_names": "სტუმრების სახელები",
      "mr": "ბატონი",
      "ms": "ქალბატონი",
      "mrs": "ქალბატონი",
      "guest_name_placeholder": "სტუმრის სახელი",
      "package": "პაკეტი",
      "standard_package": "სტანდარტული პაკეტი",
      "premium_package": "პრემიუმ პაკეტი",
      "luxury_package": "ლუქს პაკეტი",
      "book_now": "დაჯავშნა",
      "organized_by": "ორგანიზებულია",
      "booking_submitted": "დაჯავშნის მოთხოვნა გაგზავნილია! ჩვენ მალე დაგიკავშირდებით.",
      "please_complete_contact": "გთხოვთ შეავსოთ თქვენი საკონტაქტო ინფორმაცია ქვემოთ დაჯავშნის დასასრულებლად.",
      "complete_booking": "დაასრულეთ თქვენი დაჯავშნა",
      "contact_description": "გთხოვთ მიუთითოთ თქვენი საკონტაქტო ინფორმაცია დაჯავშნის მოთხოვნის დასასრულებლად.",
      "additional_requests": "დამატებითი მოთხოვნები ან კითხვები",
      "send_booking_request": "დაჯავშნის მოთხოვნის გაგზავნა",
      "booking_details": "დაჯავშნის დეტალები",
      "product": "პროდუქტი",
      "total_guests": "სტუმრების საერთო რაოდენობა",
      "booking_time": "დაჯავშნის დრო",
      "contact_form_not_found": "საკონტაქტო ფორმა ვერ მოიძებნა. გთხოვთ გადახვიდეთ საკონტაქტო გვერდზე დაჯავშნის დასასრულებლად."
    },
    "tour": {
      "highlights_title": "ტურის მთავარი მახასიათებლები",
      "highlight_1_title": "პროფესიონალი გიდი",
      "highlight_1_desc": "ექსპერტი ადგილობრივი გიდი ღრმა ცოდნით",
      "highlight_2_title": "ყველაფერი ჩართული",
      "highlight_2_desc": "ტრანსპორტი, კვება და შესვლის ბილეთები ჩართული",
      "highlight_3_title": "მცირე ჯგუფები",
      "highlight_3_desc": "მაქსიმუმ 12 ადამიანი პერსონალიზებული გამოცდილებისთვის",
      "highlight_4_title": "მოქნილი დრო",
      "highlight_4_desc": "მრავალი გამგზავრების დრო ხელმისაწვდომია ყოველდღე",
      "safety_title": "უსაფრთხოება და კომფორტი",
      "safety_1": "დაზღვეული",
      "safety_2": "ლიცენზირებული",
      "safety_3": "5-ვარსკვლავიანი რეიტინგი"
    }
  },
  "localization": {
    "country_label": "ქვეყანა/რეგიონი",
    "language_label": "ენა",
    "update_language": "ენის განახლება",
    "update_country": "ქვეყნის/რეგიონის განახლება",
    "search": "ძიება",
    "popular_countries_regions": "პოპულარული ქვეყნები/რეგიონები",
    "country_results_count": "{{ count }} ქვეყანა/რეგიონი მოიძებნა"
  }
}
