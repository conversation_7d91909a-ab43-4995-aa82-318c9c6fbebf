.metropole-gallery-section {
  margin: 0;
}

.metropole-gallery-container {
  margin-top: 1rem;
}

/* Product page specific styles */
.product-metropole-gallery {
  margin: 0;
}

.product-metropole-gallery .metropole-gallery-container {
  margin-top: 0;
}

/* Booking.com-like button alignment above gallery */
.product-metropole-gallery {
  width: 100%;
}

/* Keep gallery full width inside page container */
.page-width > .product-with-booking + .product-metropole-gallery,
.page-width .product-metropole-gallery {
  display: block;
}

/* Exact Metropole Bangkok Style */
.metropole-gallery {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4px;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.metropole-main {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.metropole-main img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.metropole-main:hover img {
  transform: scale(1.02);
}

.metropole-thumbnails {
  display: grid;
  grid-template-rows: repeat(2, 1fr);
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
}

.metropole-thumb {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.metropole-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.metropole-thumb:hover img {
  transform: scale(1.05);
}

/* Show all photos overlay on last thumbnail */
.metropole-thumb:last-child::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;
}

.metropole-thumb:last-child::after {
  content: 'See All Photos';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  z-index: 2;
  text-align: center;
  line-height: 1.3;
}

.metropole-thumb:last-child:hover::before {
  background: rgba(0, 0, 0, 0.7);
}

/* Modal styles */
.metropole-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.metropole-modal.active {
  opacity: 1;
  visibility: visible;
}

.metropole-modal-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.metropole-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.metropole-modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.metropole-modal-counter {
  font-size: 1rem;
  color: #666;
  margin-top: 0.25rem;
}

.metropole-modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  line-height: 1;
}

.metropole-modal-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.metropole-modal-main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 2rem;
}

.metropole-modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.metropole-modal-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #333;
  transition: all 0.2s ease;
  line-height: 1;
}

.metropole-modal-prev {
  left: 2rem;
}

.metropole-modal-next {
  right: 2rem;
}

.metropole-modal-nav:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
}

.metropole-modal-thumbnails {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  overflow-x: auto;
}

.metropole-modal-thumbs-container {
  display: flex;
  gap: 0.75rem;
}

.metropole-modal-thumb {
  flex-shrink: 0;
  width: 80px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.metropole-modal-thumb.active {
  border-color: #0066cc;
}

.metropole-modal-thumb:hover {
  border-color: #ccc;
}

.metropole-modal-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media screen and (max-width: 768px) {
  .metropole-gallery {
    grid-template-columns: 1fr;
    height: auto;
  }

  .metropole-main {
    height: 250px;
  }

  .metropole-thumbnails {
    grid-template-rows: 1fr;
    grid-template-columns: repeat(4, 1fr);
    height: 80px;
    margin-top: 4px;
  }

  .metropole-modal-header {
    padding: 0.75rem 1rem;
  }

  .metropole-modal-title {
    font-size: 1.25rem;
  }

  .metropole-modal-main {
    padding: 1rem;
  }

  .metropole-modal-nav {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .metropole-modal-prev {
    left: 1rem;
  }

  .metropole-modal-next {
    right: 1rem;
  }

  .metropole-modal-thumbnails {
    padding: 0.75rem 1rem;
  }

  .metropole-modal-thumb {
    width: 60px;
    height: 45px;
  }
}

/* Custom element wrapper */
metropole-gallery {
  display: block;
}
