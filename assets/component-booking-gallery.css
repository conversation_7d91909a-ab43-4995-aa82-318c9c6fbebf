.booking-gallery-section {
  margin: 0;
}

.booking-gallery-container {
  margin-top: 2rem;
}

.booking-gallery-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
}

@media screen and (max-width: 749px) {
  .booking-gallery-grid {
    grid-template-columns: 1fr;
    height: auto;
  }
}

.booking-gallery-main {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.booking-gallery-main-image {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.booking-gallery-main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.booking-gallery-main:hover .booking-gallery-main-image img {
  transform: scale(1.05);
}

.booking-gallery-side {
  display: grid;
  grid-template-rows: repeat(2, 1fr);
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

@media screen and (max-width: 749px) {
  .booking-gallery-side {
    grid-template-rows: 1fr;
    grid-template-columns: repeat(4, 1fr);
    height: 100px;
    margin-top: 8px;
  }
}

.booking-gallery-thumb {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border-radius: 4px;
}

.booking-gallery-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.booking-gallery-thumb:hover img {
  transform: scale(1.1);
}

.booking-gallery-more-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking-gallery-show-all {
  background: transparent;
  border: 2px solid white;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.booking-gallery-show-all:hover {
  background: white;
  color: #333;
  transform: scale(1.05);
}

.booking-gallery-plus {
  font-size: 1.5rem;
  font-weight: bold;
}

.booking-gallery-text {
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Modal Styles */
.booking-gallery-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.booking-gallery-modal.active {
  opacity: 1;
  visibility: visible;
}

.booking-gallery-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
}

.booking-gallery-modal-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.booking-gallery-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e0e0e0;
  background: white;
  z-index: 10;
}

.booking-gallery-modal-info h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.booking-gallery-modal-counter {
  font-size: 1rem;
  color: #666;
  margin-top: 0.25rem;
}

.booking-gallery-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  color: #666;
}

.booking-gallery-modal-close:hover {
  background: #f5f5f5;
  color: #333;
}

.booking-gallery-modal-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.booking-gallery-modal-main {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  background: #000;
}

.booking-gallery-modal-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.booking-gallery-modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.booking-gallery-modal-prev,
.booking-gallery-modal-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
  color: #333;
}

.booking-gallery-modal-prev {
  left: 2rem;
}

.booking-gallery-modal-next {
  right: 2rem;
}

.booking-gallery-modal-prev:hover,
.booking-gallery-modal-next:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
}

.booking-gallery-modal-thumbnails {
  background: white;
  border-top: 1px solid #e0e0e0;
  padding: 1rem 2rem;
  overflow: hidden;
}

.booking-gallery-modal-thumbnails-container {
  display: flex;
  gap: 0.75rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.booking-gallery-modal-thumb {
  flex-shrink: 0;
  width: 100px;
  height: 75px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.2s ease;
}

.booking-gallery-modal-thumb.active {
  border-color: #0071c2;
}

.booking-gallery-modal-thumb:hover {
  border-color: #ccc;
}

.booking-gallery-modal-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media screen and (max-width: 749px) {
  .booking-gallery-modal-header {
    padding: 0.75rem 1rem;
  }
  
  .booking-gallery-modal-info h3 {
    font-size: 1.25rem;
  }
  
  .booking-gallery-modal-image-container {
    padding: 1rem;
  }
  
  .booking-gallery-modal-prev,
  .booking-gallery-modal-next {
    width: 50px;
    height: 50px;
  }
  
  .booking-gallery-modal-prev {
    left: 1rem;
  }
  
  .booking-gallery-modal-next {
    right: 1rem;
  }
  
  .booking-gallery-modal-thumbnails {
    padding: 0.75rem 1rem;
  }
  
  .booking-gallery-modal-thumb {
    width: 80px;
    height: 60px;
  }
}

/* Custom element wrapper */
booking-gallery {
  display: block;
}
