.hotel-gallery-section {
  margin: 0;
}

.hotel-gallery-container {
  margin-top: 2rem;
}

.hotel-gallery-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4px;
  height: 450px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: box-shadow 0.3s ease;
}

.hotel-gallery-grid:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

@media screen and (max-width: 749px) {
  .hotel-gallery-grid {
    grid-template-columns: 1fr;
    height: auto;
  }
}

.hotel-gallery-main {
  position: relative;
  overflow: hidden;
}

.hotel-gallery-main-image {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
  overflow: hidden;
}

.hotel-gallery-main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.hotel-gallery-main-image:hover img {
  transform: scale(1.08);
}

.hotel-gallery-main-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.hotel-gallery-main-image:hover::before {
  opacity: 1;
}

.hotel-gallery-overlay {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  z-index: 2;
}

.hotel-gallery-view-all {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.75rem 1.25rem;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.hotel-gallery-view-all:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hotel-gallery-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hotel-gallery-icon svg {
  width: 16px;
  height: 16px;
}

.hotel-gallery-thumbnails {
  display: grid;
  grid-template-rows: repeat(2, 1fr);
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
}

@media screen and (max-width: 749px) {
  .hotel-gallery-thumbnails {
    grid-template-rows: 1fr;
    grid-template-columns: repeat(4, 1fr);
    height: 100px;
    margin-top: 0.5rem;
  }
}

.hotel-gallery-thumb {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border-radius: 4px;
}

.hotel-gallery-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.hotel-gallery-thumb:hover img {
  transform: scale(1.15);
}

.hotel-gallery-thumb::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.hotel-gallery-thumb:hover::before {
  opacity: 1;
}

.hotel-gallery-more-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

/* Modal Styles */
.hotel-gallery-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.hotel-gallery-modal.active {
  opacity: 1;
  visibility: visible;
}

.hotel-gallery-modal-content {
  width: 90%;
  max-width: 1200px;
  height: 90%;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.hotel-gallery-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #eee;
}

.hotel-gallery-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.hotel-gallery-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.hotel-gallery-modal-close:hover {
  background: #f5f5f5;
}

.hotel-gallery-modal-close .svg-wrapper {
  width: 20px;
  height: 20px;
}

.hotel-gallery-modal-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.hotel-gallery-modal-main {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  background: #f8f9fa;
}

.hotel-gallery-modal-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.hotel-gallery-modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.hotel-gallery-modal-prev,
.hotel-gallery-modal-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.hotel-gallery-modal-prev {
  left: 1rem;
}

.hotel-gallery-modal-next {
  right: 1rem;
}

.hotel-gallery-modal-prev:hover,
.hotel-gallery-modal-next:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
}

.hotel-gallery-modal-prev .svg-wrapper {
  transform: rotate(90deg);
}

.hotel-gallery-modal-next .svg-wrapper {
  transform: rotate(-90deg);
}

.hotel-gallery-modal-thumbnails {
  display: flex;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  overflow-x: auto;
  border-top: 1px solid #eee;
  background: white;
}

.hotel-gallery-modal-thumb {
  flex-shrink: 0;
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.hotel-gallery-modal-thumb.active {
  border-color: #007bff;
}

.hotel-gallery-modal-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media screen and (max-width: 749px) {
  .hotel-gallery-modal-content {
    width: 95%;
    height: 95%;
  }
  
  .hotel-gallery-modal-header {
    padding: 0.75rem 1rem;
  }
  
  .hotel-gallery-modal-image-container {
    padding: 1rem;
  }
  
  .hotel-gallery-modal-prev,
  .hotel-gallery-modal-next {
    width: 40px;
    height: 40px;
  }
  
  .hotel-gallery-modal-thumbnails {
    padding: 0.75rem 1rem;
  }
  
  .hotel-gallery-modal-thumb {
    width: 60px;
    height: 45px;
  }
}

/* Custom element wrapper */
hotel-gallery {
  display: block;
}
