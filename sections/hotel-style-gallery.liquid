{{ 'component-hotel-gallery.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<hotel-gallery class="hotel-gallery-section section-{{ section.id }}-padding">
  <div class="page-width">
    {%- if section.settings.title != blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin">
        <h2 class="title inline-richtext {{ section.settings.heading_size }}">
          {{ section.settings.title }}
        </h2>
      </div>
    {%- endif -%}

    <div class="hotel-gallery-container">
      {%- if section.blocks.size > 0 -%}
        <div class="hotel-gallery-grid">
          {%- assign main_image = section.blocks.first -%}
          
          {%- if main_image.settings.image != blank -%}
            <div class="hotel-gallery-main">
              <div class="hotel-gallery-main-image" data-gallery-item="0">
                <img
                  src="{{ main_image.settings.image | image_url: width: 800 }}"
                  alt="{{ main_image.settings.image.alt | escape }}"
                  loading="lazy"
                  width="{{ main_image.settings.image.width }}"
                  height="{{ main_image.settings.image.height }}"
                >
                {%- if section.blocks.size > 1 -%}
                  <div class="hotel-gallery-overlay">
                    <button class="hotel-gallery-view-all" type="button">
                      <span class="hotel-gallery-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                          <circle cx="8.5" cy="8.5" r="1.5"/>
                          <polyline points="21,15 16,10 5,21"/>
                        </svg>
                      </span>
                      <span>See All {{ section.blocks.size }} Photos</span>
                    </button>
                  </div>
                {%- endif -%}
              </div>
            </div>
          {%- endif -%}

          {%- if section.blocks.size > 1 -%}
            <div class="hotel-gallery-thumbnails">
              {%- for block in section.blocks limit: 5 offset: 1 -%}
                {%- if block.settings.image != blank -%}
                  <div class="hotel-gallery-thumb" data-gallery-item="{{ forloop.index }}">
                    <img
                      src="{{ block.settings.image | image_url: width: 300 }}"
                      alt="{{ block.settings.image.alt | escape }}"
                      loading="lazy"
                      width="{{ block.settings.image.width }}"
                      height="{{ block.settings.image.height }}"
                    >
                    {%- if forloop.last and section.blocks.size > 5 -%}
                      <div class="hotel-gallery-more-overlay">
                        <span>+{{ section.blocks.size | minus: 5 }}</span>
                      </div>
                    {%- endif -%}
                  </div>
                {%- endif -%}
              {%- endfor -%}
            </div>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>

    <!-- Modal for full gallery view -->
    <div class="hotel-gallery-modal" id="gallery-modal-{{ section.id }}">
      <div class="hotel-gallery-modal-content">
        <div class="hotel-gallery-modal-header">
          <h3>Gallery</h3>
          <button class="hotel-gallery-modal-close" type="button">
            <span class="svg-wrapper">
              {{- 'icon-close.svg' | inline_asset_content -}}
            </span>
          </button>
        </div>
        <div class="hotel-gallery-modal-body">
          <div class="hotel-gallery-modal-main">
            <button class="hotel-gallery-modal-prev" type="button">
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
            <div class="hotel-gallery-modal-image-container">
              <img class="hotel-gallery-modal-image" src="" alt="">
            </div>
            <button class="hotel-gallery-modal-next" type="button">
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
          </div>
          <div class="hotel-gallery-modal-thumbnails">
            {%- for block in section.blocks -%}
              {%- if block.settings.image != blank -%}
                <div class="hotel-gallery-modal-thumb" data-index="{{ forloop.index0 }}">
                  <img
                    src="{{ block.settings.image | image_url: width: 100 }}"
                    alt="{{ block.settings.image.alt | escape }}"
                    data-full-src="{{ block.settings.image | image_url: width: 1200 }}"
                  >
                </div>
              {%- endif -%}
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  </div>
</hotel-gallery>

<script>
  class HotelGallery extends HTMLElement {
    constructor() {
      super();
      this.modal = this.querySelector('.hotel-gallery-modal');
      this.modalImage = this.querySelector('.hotel-gallery-modal-image');
      this.modalThumbs = this.querySelectorAll('.hotel-gallery-modal-thumb');
      this.currentIndex = 0;
      this.images = [];
      
      // Collect all images
      this.modalThumbs.forEach((thumb, index) => {
        const img = thumb.querySelector('img');
        this.images.push({
          src: img.dataset.fullSrc,
          alt: img.alt
        });
      });
      
      this.bindEvents();
    }
    
    bindEvents() {
      // Open modal
      this.querySelector('.hotel-gallery-view-all')?.addEventListener('click', () => {
        this.openModal(0);
      });
      
      // Gallery items click
      this.querySelectorAll('[data-gallery-item]').forEach(item => {
        item.addEventListener('click', (e) => {
          const index = parseInt(e.currentTarget.dataset.galleryItem);
          this.openModal(index);
        });
      });
      
      // Modal controls
      this.querySelector('.hotel-gallery-modal-close')?.addEventListener('click', () => {
        this.closeModal();
      });
      
      this.querySelector('.hotel-gallery-modal-prev')?.addEventListener('click', () => {
        this.prevImage();
      });
      
      this.querySelector('.hotel-gallery-modal-next')?.addEventListener('click', () => {
        this.nextImage();
      });
      
      // Thumbnail clicks
      this.modalThumbs.forEach((thumb, index) => {
        thumb.addEventListener('click', () => {
          this.showImage(index);
        });
      });
      
      // Close on backdrop click
      this.modal?.addEventListener('click', (e) => {
        if (e.target === this.modal) {
          this.closeModal();
        }
      });
      
      // Keyboard navigation
      document.addEventListener('keydown', (e) => {
        if (!this.modal.classList.contains('active')) return;
        
        switch(e.key) {
          case 'Escape':
            this.closeModal();
            break;
          case 'ArrowLeft':
            this.prevImage();
            break;
          case 'ArrowRight':
            this.nextImage();
            break;
        }
      });
    }
    
    openModal(index = 0) {
      this.currentIndex = index;
      this.showImage(index);
      this.modal.classList.add('active');
      document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
      this.modal.classList.remove('active');
      document.body.style.overflow = '';
    }
    
    showImage(index) {
      if (index < 0 || index >= this.images.length) return;
      
      this.currentIndex = index;
      this.modalImage.src = this.images[index].src;
      this.modalImage.alt = this.images[index].alt;
      
      // Update active thumbnail
      this.modalThumbs.forEach((thumb, i) => {
        thumb.classList.toggle('active', i === index);
      });
    }
    
    nextImage() {
      const nextIndex = (this.currentIndex + 1) % this.images.length;
      this.showImage(nextIndex);
    }
    
    prevImage() {
      const prevIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
      this.showImage(prevIndex);
    }
  }
  
  customElements.define('hotel-gallery', HotelGallery);
</script>

{% schema %}
{
  "name": "Hotel Style Gallery",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Gallery",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Hotel Style Gallery",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ]
}
{% endschema %}
