{{ 'section-contact-form.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="contact page-width page-width--narrow section-{{ section.id }}-padding">
    {%- if section.settings.heading != blank -%}
      <h2 class="title title-wrapper--no-top-margin inline-richtext {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        {{ section.settings.heading }}
      </h2>
    {%- else -%}
      <h2 class="visually-hidden">{{ 'templates.contact.form.title' | t }}</h2>
    {%- endif -%}
    {%- liquid
      assign contact_form_class = 'isolate'
      if settings.animations_reveal_on_scroll
        assign contact_form_class = 'isolate scroll-trigger animate--slide-in'
      endif
    -%}
    {%- form 'contact', id: 'ContactForm', class: contact_form_class -%}
      <!-- Hidden fields for booking data and page information -->
      <input type="hidden" id="booking-data" name="contact[booking_details]" value="">
      <input type="hidden" id="page-title" name="contact[page_title]" value="">
      <input type="hidden" id="source-page" name="contact[source_page]" value="{% if page.handle %}{{ page.handle }}{% elsif request.page_type == 'index' %}homepage{% elsif request.page_type == 'product' %}{{ product.handle }}{% elsif request.page_type == 'collection' %}{{ collection.handle }}{% else %}{{ request.page_type }}{% endif %}">
      <input type="hidden" id="page-url" name="contact[page_url]" value="{{ request.origin }}{{ request.path }}">
      <input type="hidden" id="page-name" name="contact[page_name]" value="{% if page.title %}{{ page.title }}{% elsif request.page_type == 'index' %}{{ shop.name }} - Homepage{% elsif request.page_type == 'product' %}{{ product.title }}{% elsif request.page_type == 'collection' %}{{ collection.title }}{% else %}{{ page_title }}{% endif %}">

      {%- if form.posted_successfully? -%}
        <h2 class="form-status form-status-list form__message" tabindex="-1" autofocus>
          {{- 'icon-success.svg' | inline_asset_content -}}
          {{ 'templates.contact.form.post_success' | t }}
        </h2>
      {%- elsif form.errors -%}
        <div class="form__message">
          <h2 class="form-status caption-large text-body" role="alert" tabindex="-1" autofocus>
            {{- 'icon-error.svg' | inline_asset_content -}}
            {{ 'templates.contact.form.error_heading' | t }}
          </h2>
        </div>
        <ul class="form-status-list caption-large" role="list">
          <li>
            <a href="#ContactForm-email" class="link">
              {{ form.errors.translated_fields.email | capitalize }}
              {{ form.errors.messages.email }}
            </a>
          </li>
        </ul>
      {%- endif -%}
      <div class="contact__fields">
        <div class="field">
          <input
            class="field__input"
            autocomplete="name"
            type="text"
            id="ContactForm-name"
            name="contact[{{ 'templates.contact.form.name' | t }}]"
            value="{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}"
            placeholder="{{ 'templates.contact.form.name' | t }}"
          >
          <label class="field__label" for="ContactForm-name">{{ 'templates.contact.form.name' | t }}</label>
        </div>
        <div class="field field--with-error">
          <input
            autocomplete="email"
            type="email"
            id="ContactForm-email"
            class="field__input"
            name="contact[email]"
            spellcheck="false"
            autocapitalize="off"
            value="{% if form.email %}{{ form.email }}{% elsif customer %}{{ customer.email }}{% endif %}"
            aria-required="true"
            {% if form.errors contains 'email' %}
              aria-invalid="true"
              aria-describedby="ContactForm-email-error"
            {% endif %}
            placeholder="{{ 'templates.contact.form.email' | t }}"
          >
          <label class="field__label" for="ContactForm-email">
            {{- 'templates.contact.form.email' | t }}
            <span aria-hidden="true">*</span></label
          >
          {%- if form.errors contains 'email' -%}
            <small class="contact__field-error" id="ContactForm-email-error">
              <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
              <span class="form__message">
                <span class="svg-wrapper">
                  {{- 'icon-error.svg' | inline_asset_content -}}
                </span>
                {{- form.errors.translated_fields.email | capitalize }}
                {{ form.errors.messages.email -}}
              </span>
            </small>
          {%- endif -%}
        </div>
      </div>
      <div class="field">
        <input
          type="tel"
          id="ContactForm-phone"
          class="field__input"
          autocomplete="tel"
          name="contact[{{ 'templates.contact.form.phone' | t }}]"
          pattern="[\+]?[0-9\s\-\(\)]*"
          value="{% if form.phone %}{{ form.phone }}{% elsif customer %}{{ customer.phone }}{% endif %}"
          placeholder="{{ 'templates.contact.form.phone' | t }}"
        >
        <label class="field__label" for="ContactForm-phone">{{ 'templates.contact.form.phone' | t }}</label>
      </div>
      <div class="field">
        <textarea
          rows="10"
          id="ContactForm-body"
          class="text-area field__input"
          name="contact[{{ 'templates.contact.form.comment' | t }}]"
          placeholder="{{ 'templates.contact.form.comment' | t }}"
        >
          {{- form.body -}}
        </textarea>
        <label class="form__label field__label" for="ContactForm-body">
          {{- 'templates.contact.form.comment' | t -}}
        </label>
      </div>
      <div class="contact__button">
        <button type="submit" class="button">
          {{ 'templates.contact.form.send' | t }}
        </button>
      </div>
    {%- endform -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.contact-form.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "t:sections.contact-form.settings.title.default",
      "label": "t:sections.contact-form.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "t:sections.contact-form.presets.name"
    }
  ]
}
{% endschema %}
