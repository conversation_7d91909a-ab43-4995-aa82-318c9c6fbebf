{{ 'component-booking-gallery.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<booking-gallery class="booking-gallery-section section-{{ section.id }}-padding">
  <div class="page-width">
    {%- if section.settings.title != blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin">
        <h2 class="title inline-richtext {{ section.settings.heading_size }}">
          {{ section.settings.title }}
        </h2>
      </div>
    {%- endif -%}

    <div class="booking-gallery-container">
      {%- if section.blocks.size > 0 -%}
        <div class="booking-gallery-grid">
          {%- assign main_image = section.blocks.first -%}
          
          {%- if main_image.settings.image != blank -%}
            <div class="booking-gallery-main">
              <div class="booking-gallery-main-image" data-gallery-item="0">
                <img
                  src="{{ main_image.settings.image | image_url: width: 800 }}"
                  alt="{{ main_image.settings.image.alt | escape }}"
                  loading="lazy"
                  width="{{ main_image.settings.image.width }}"
                  height="{{ main_image.settings.image.height }}"
                >
              </div>
            </div>
          {%- endif -%}

          {%- if section.blocks.size > 1 -%}
            <div class="booking-gallery-side">
              {%- for block in section.blocks limit: 4 offset: 1 -%}
                {%- if block.settings.image != blank -%}
                  <div class="booking-gallery-thumb" data-gallery-item="{{ forloop.index }}">
                    <img
                      src="{{ block.settings.image | image_url: width: 300 }}"
                      alt="{{ block.settings.image.alt | escape }}"
                      loading="lazy"
                      width="{{ block.settings.image.width }}"
                      height="{{ block.settings.image.height }}"
                    >
                    {%- if forloop.last and section.blocks.size > 5 -%}
                      <div class="booking-gallery-more-overlay">
                        <button class="booking-gallery-show-all" type="button">
                          <span class="booking-gallery-plus">+{{ section.blocks.size | minus: 5 }}</span>
                          <span class="booking-gallery-text">Show all photos</span>
                        </button>
                      </div>
                    {%- endif -%}
                  </div>
                {%- endif -%}
              {%- endfor -%}
            </div>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>

    <!-- Modal for full gallery view -->
    <div class="booking-gallery-modal" id="gallery-modal-{{ section.id }}">
      <div class="booking-gallery-modal-backdrop"></div>
      <div class="booking-gallery-modal-content">
        <div class="booking-gallery-modal-header">
          <div class="booking-gallery-modal-info">
            <h3>Photos</h3>
            <span class="booking-gallery-counter">
              <span class="current">1</span> / <span class="total">{{ section.blocks.size }}</span>
            </span>
          </div>
          <button class="booking-gallery-modal-close" type="button">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="booking-gallery-modal-body">
          <div class="booking-gallery-modal-main">
            <button class="booking-gallery-modal-prev" type="button">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="15,18 9,12 15,6"></polyline>
              </svg>
            </button>
            <div class="booking-gallery-modal-image-container">
              <img class="booking-gallery-modal-image" src="" alt="">
            </div>
            <button class="booking-gallery-modal-next" type="button">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </button>
          </div>
          <div class="booking-gallery-modal-thumbnails">
            <div class="booking-gallery-modal-thumbnails-container">
              {%- for block in section.blocks -%}
                {%- if block.settings.image != blank -%}
                  <div class="booking-gallery-modal-thumb" data-index="{{ forloop.index0 }}">
                    <img
                      src="{{ block.settings.image | image_url: width: 120 }}"
                      alt="{{ block.settings.image.alt | escape }}"
                      data-full-src="{{ block.settings.image | image_url: width: 1200 }}"
                    >
                  </div>
                {%- endif -%}
              {%- endfor -%}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</booking-gallery>

<script>
  class BookingGallery extends HTMLElement {
    constructor() {
      super();
      this.modal = this.querySelector('.booking-gallery-modal');
      this.modalImage = this.querySelector('.booking-gallery-modal-image');
      this.modalThumbs = this.querySelectorAll('.booking-gallery-modal-thumb');
      this.currentCounter = this.querySelector('.current');
      this.totalCounter = this.querySelector('.total');
      this.currentIndex = 0;
      this.images = [];
      
      // Collect all images
      this.modalThumbs.forEach((thumb, index) => {
        const img = thumb.querySelector('img');
        this.images.push({
          src: img.dataset.fullSrc,
          alt: img.alt
        });
      });
      
      this.bindEvents();
    }
    
    bindEvents() {
      // Open modal from show all button
      this.querySelector('.booking-gallery-show-all')?.addEventListener('click', () => {
        this.openModal(0);
      });
      
      // Gallery items click
      this.querySelectorAll('[data-gallery-item]').forEach(item => {
        item.addEventListener('click', (e) => {
          const index = parseInt(e.currentTarget.dataset.galleryItem);
          this.openModal(index);
        });
      });
      
      // Modal controls
      this.querySelector('.booking-gallery-modal-close')?.addEventListener('click', () => {
        this.closeModal();
      });
      
      this.querySelector('.booking-gallery-modal-prev')?.addEventListener('click', () => {
        this.prevImage();
      });
      
      this.querySelector('.booking-gallery-modal-next')?.addEventListener('click', () => {
        this.nextImage();
      });
      
      // Thumbnail clicks
      this.modalThumbs.forEach((thumb, index) => {
        thumb.addEventListener('click', () => {
          this.showImage(index);
        });
      });
      
      // Close on backdrop click
      this.querySelector('.booking-gallery-modal-backdrop')?.addEventListener('click', () => {
        this.closeModal();
      });
      
      // Keyboard navigation
      document.addEventListener('keydown', (e) => {
        if (!this.modal.classList.contains('active')) return;
        
        switch(e.key) {
          case 'Escape':
            this.closeModal();
            break;
          case 'ArrowLeft':
            this.prevImage();
            break;
          case 'ArrowRight':
            this.nextImage();
            break;
        }
      });
    }
    
    openModal(index = 0) {
      this.currentIndex = index;
      this.showImage(index);
      this.modal.classList.add('active');
      document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
      this.modal.classList.remove('active');
      document.body.style.overflow = '';
    }
    
    showImage(index) {
      if (index < 0 || index >= this.images.length) return;
      
      this.currentIndex = index;
      this.modalImage.src = this.images[index].src;
      this.modalImage.alt = this.images[index].alt;
      
      // Update counter
      if (this.currentCounter) {
        this.currentCounter.textContent = index + 1;
      }
      
      // Update active thumbnail
      this.modalThumbs.forEach((thumb, i) => {
        thumb.classList.toggle('active', i === index);
      });
      
      // Scroll active thumbnail into view
      const activeThumb = this.modalThumbs[index];
      if (activeThumb) {
        activeThumb.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
      }
    }
    
    nextImage() {
      const nextIndex = (this.currentIndex + 1) % this.images.length;
      this.showImage(nextIndex);
    }
    
    prevImage() {
      const prevIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
      this.showImage(prevIndex);
    }
  }
  
  customElements.define('booking-gallery', BookingGallery);
</script>

{% schema %}
{
  "name": "Booking Style Gallery",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Gallery",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Booking Style Gallery",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ]
}
{% endschema %}
