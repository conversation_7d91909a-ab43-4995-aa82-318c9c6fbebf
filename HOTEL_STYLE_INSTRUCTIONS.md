# სასტუმროს სტილის პროდუქტის გვერდის ინსტრუქციები

## რა შეიქმნა

თქვენი მთავარი პროდუქტის გვერდი ახლა ზუსტად ისეთივე გამოიყურება, როგორც სურათზე ნაჩვენები სასტუმროს ბუკინგის გვერდი. ეს მოიცავს:

### ✅ შექმნილი კომპონენტები:

1. **სასტუმროს სტილის Header**
   - პროდუქტის სათაური (Metropole Bangkok)
   - 5 ვარსკვლავიანი რეიტინგი
   - მისამართი "Show on map" ლინკით
   - "We Price Match" ბეჯი
   - ლურჯი ფასის ღილაკი (ჩანაცვლებული "Select Rooms"-ის)

2. **სურათების გალერეა**
   - დიდი მთავარი სურათი მარცხნივ
   - 4 პატარა thumbnail სურათი მარჯვნივ
   - "See all photos" ღილაკი

3. **ნავიგაციის ტაბები**
   - Overview (აქტიური)
   - Rooms
   - Guest Reviews
   - Services & Amenities
   - Policies
   - Location

4. **Highlights სექცია**
   - Low-rise building
   - Free parking
   - Delicious breakfast
   - Lots to do

5. **Amenities სექცია**
   - Outdoor swimming pool
   - Sauna
   - Gym
   - Bar
   - Restaurant
   - Taxi booking service
   - Public parking (Free)
   - Conference room

6. **Rating სექცია (Sidebar)**
   - 8.2/10 Very Good რეიტინგი
   - აღწერილობა
   - "All 1,521 reviews" ლინკი

7. **Surroundings სექცია (Sidebar)**
   - Metro: Ramkhamhaeng (1.9 km)
   - Metro: Thong Lo (2.8 km)
   - Airport: Suvarnabhumi Airport (24.9 km)
   - Airport: Don Mueang International Airport (25.0 km)

## როგორ გამოვიყენოთ

### 1. ახალი დიზაინის ჩართვა

ახალი სასტუმროს სტილის დიზაინი უკვე ჩართულია თქვენს `sections/main-product.liquid` ფაილში. ის ავტომატურად გამოჩნდება ყველა პროდუქტის გვერდზე.

### 2. ფაილები რომლებიც შეიქმნა/შეიცვალა:

- `snippets/hotel-style-product.liquid` - ახალი სასტუმროს სტილის კომპონენტი
- `assets/section-main-product.css` - დამატებული CSS სტილები
- `sections/main-product.liquid` - განახლებული მთავარი სექცია
- `test-hotel-style-product.html` - ტესტის ფაილი

### 3. კასტომიზაცია

#### პროდუქტის ინფორმაციის შეცვლა:
```liquid
<!-- მისამართის შეცვლა -->
{{ product.metafields.custom.address | default: "თქვენი მისამართი" }}

<!-- რეიტინგის შეცვლა -->
<div class="rating-score">8.2</div> <!-- შეცვალეთ ციფრი -->
<div class="rating-label">Very Good</div> <!-- შეცვალეთ ტექსტი -->
```

#### Highlights-ის შეცვლა:
`snippets/hotel-style-product.liquid` ფაილში შეცვალეთ:
```liquid
<span class="highlight-text">თქვენი ტექსტი</span>
```

#### Amenities-ის დამატება/შეცვლა:
```liquid
<div class="amenity-item">
  <svg class="amenity-icon"><!-- თქვენი აიკონი --></svg>
  <span class="amenity-text">თქვენი amenity</span>
</div>
```

### 4. რესპონსიული დიზაინი

დიზაინი სრულად რესპონსიულია:
- **Desktop**: 2-სვეტიანი layout
- **Tablet**: 1-სვეტიანი layout
- **Mobile**: ოპტიმიზებული მობილურისთვის

### 5. ტესტირება

გახსენით `test-hotel-style-product.html` ბრაუზერში რომ ნახოთ როგორ გამოიყურება დიზაინი.

### 6. ორიგინალ დიზაინზე დაბრუნება

თუ გსურთ ორიგინალ დიზაინზე დაბრუნება, `sections/main-product.liquid`-ში:
1. დამალეთ: `{% render 'hotel-style-product' %}`
2. გამოაჩინეთ: `<div class="product-with-booking" style="display: none;">` → `<div class="product-with-booking">`

## დამატებითი ფუნქციები

### ტაბების ფუნქციონალობა
ტაბები მუშაობს JavaScript-ით. შეგიძლიათ დაამატოთ კონტენტი თითოეული ტაბისთვის.

### სურათების გალერეა
გალერეა ავტომატურად იყენებს პროდუქტის სურათებს. პირველი სურათი არის მთავარი, დანარჩენი 4 არის thumbnails.

### კომპაქტური ბუკინგის ფორმა
ფორმა შეიცავს:
- **Date**: ბუკინგის თარიღი
- **Adults**: მოზრდილები (1-4, Age 18+, სრული ფასი)
- **Children**: ბავშვები (0-2, Age 6-17, 50% ფასი)
- **Guest Names**: ავტომატური ველები მგზავრების რაოდენობის მიხედვით
- **Book Now**: ბუკინგის დადასტურება

**ლიმიტები:**
- მაქსიმუმ 4 ადამიანი სულ (Adults + Children)
- ფასი ავტომატურად იცვლება რაოდენობის მიხედვით
- კომპაქტური ზომა (320px max-width)

**ფასის ლოგიკა:**
- Base price არის ერთი მოზრდილის ფასი
- ბავშვები იხდიან 50% ფასს

## ბოლო განახლება

✅ **კომპაქტური ბუკინგის ფორმა დამატებულია!**
- Rating section ჩანაცვლებულია კომპაქტური ბუკინგის ფორმით
- ფორმა შეიცავს: თარიღს, Adults, Children, Guest names
- მაქსიმუმ 4 ადამიანი (Adults + Children)
- ფასი ავტომატურად იცვლება მგზავრების რაოდენობის მიხედვით
- ბავშვები იხდიან 50% ფასს
- Guest names ველები ავტომატურად ემატება/იშლება
- კომპაქტური დიზაინი (320px max-width)
- სრული ფუნქციონალობა JavaScript-ით

## მხარდაჭერა

ყველა ფაილი შექმნილია და მზადაა გამოსაყენებლად. დიზაინი ზუსტად იმეორებს სურათზე ნაჩვენებ სასტუმროს ბუკინგის გვერდს, ახლა სრული ბუკინგის ფორმით რომელიც:
- ლიმიტირებულია 4 ადამიანით
- ავტომატურად ითვლის ფასს
- დინამიურად ამატებს guest name ველებს
- მუშაობს როგორც ნამდვილი ბუკინგის სისტემა
