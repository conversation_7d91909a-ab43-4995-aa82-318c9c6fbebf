<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Page with Metropole Gallery</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fff;
            line-height: 1.6;
        }
        
        .page-width {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .product {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            padding: 2rem 0;
        }
        
        @media screen and (max-width: 768px) {
            .product {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }
        
        .product__media-wrapper {
            /* Gallery styles will be applied here */
        }
        
        .product__info-wrapper {
            padding: 1rem 0;
        }
        
        .product__title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .product__price {
            font-size: 1.5rem;
            font-weight: 600;
            color: #0066cc;
            margin-bottom: 1.5rem;
        }
        
        .product__description {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.7;
        }
        
        .product__form {
            margin-bottom: 2rem;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .btn:hover {
            background: #0052a3;
        }
        
        /* Include Metropole Gallery CSS */
        .product-metropole-gallery {
            margin: 0;
        }

        .product-metropole-gallery .metropole-gallery-container {
            margin-top: 0;
        }

        .metropole-gallery {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4px;
            height: 400px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .metropole-main {
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .metropole-main img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .metropole-main:hover img {
            transform: scale(1.02);
        }

        .metropole-thumbnails {
            display: grid;
            grid-template-rows: repeat(2, 1fr);
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
        }

        .metropole-thumb {
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .metropole-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .metropole-thumb:hover img {
            transform: scale(1.05);
        }

        .metropole-thumb:last-child::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1;
        }

        .metropole-thumb:last-child::after {
            content: 'See All Photos';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 0.9rem;
            font-weight: 600;
            z-index: 2;
            text-align: center;
            line-height: 1.3;
        }

        .metropole-thumb:last-child:hover::before {
            background: rgba(0, 0, 0, 0.7);
        }

        .metropole-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .metropole-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .metropole-modal-content {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .metropole-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .metropole-modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .metropole-modal-counter {
            font-size: 1rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .metropole-modal-close {
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #666;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.2s ease;
            line-height: 1;
        }

        .metropole-modal-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #333;
        }

        .metropole-modal-main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 2rem;
        }

        .metropole-modal-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .metropole-modal-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #333;
            transition: all 0.2s ease;
            line-height: 1;
        }

        .metropole-modal-prev {
            left: 2rem;
        }

        .metropole-modal-next {
            right: 2rem;
        }

        .metropole-modal-nav:hover {
            background: white;
            transform: translateY(-50%) scale(1.1);
        }

        .metropole-modal-thumbnails {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            overflow-x: auto;
        }

        .metropole-modal-thumbs-container {
            display: flex;
            gap: 0.75rem;
        }

        .metropole-modal-thumb {
            flex-shrink: 0;
            width: 80px;
            height: 60px;
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.2s ease;
        }

        .metropole-modal-thumb.active {
            border-color: #0066cc;
        }

        .metropole-modal-thumb:hover {
            border-color: #ccc;
        }

        .metropole-modal-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        @media screen and (max-width: 768px) {
            .metropole-gallery {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .metropole-main {
                height: 250px;
            }
            
            .metropole-thumbnails {
                grid-template-rows: 1fr;
                grid-template-columns: repeat(4, 1fr);
                height: 80px;
                margin-top: 4px;
            }
        }

        metropole-gallery {
            display: block;
        }
    </style>
</head>
<body>
    <div class="page-width">
        <div class="product">
            <div class="product__media-wrapper">
                <metropole-gallery class="product-metropole-gallery">
                    <div class="metropole-gallery-container">
                        <div class="metropole-gallery">
                            <div class="metropole-main" data-index="0">
                                <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop" alt="Hotel exterior">
                            </div>
                            
                            <div class="metropole-thumbnails">
                                <div class="metropole-thumb" data-index="1">
                                    <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=300&h=200&fit=crop" alt="Hotel lobby">
                                </div>
                                <div class="metropole-thumb" data-index="2">
                                    <img src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300&h=200&fit=crop" alt="Hotel room">
                                </div>
                                <div class="metropole-thumb" data-index="3">
                                    <img src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=300&h=200&fit=crop" alt="Hotel restaurant">
                                </div>
                                <div class="metropole-thumb" data-index="4">
                                    <img src="https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=300&h=200&fit=crop" alt="Hotel pool">
                                </div>
                            </div>
                        </div>

                        <!-- Modal -->
                        <div class="metropole-modal">
                            <div class="metropole-modal-content">
                                <div class="metropole-modal-header">
                                    <div>
                                        <div class="metropole-modal-title">Around Georgia's Roads: 9 Day Jeep Tour</div>
                                        <div class="metropole-modal-counter">
                                            <span class="current">1</span> / <span class="total">5</span>
                                        </div>
                                    </div>
                                    <button class="metropole-modal-close" type="button">×</button>
                                </div>
                                
                                <div class="metropole-modal-main">
                                    <button class="metropole-modal-nav metropole-modal-prev" type="button">‹</button>
                                    <img class="metropole-modal-image" src="" alt="">
                                    <button class="metropole-modal-nav metropole-modal-next" type="button">›</button>
                                </div>
                                
                                <div class="metropole-modal-thumbnails">
                                    <div class="metropole-modal-thumbs-container">
                                        <div class="metropole-modal-thumb" data-index="0">
                                            <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=80&h=60&fit=crop" alt="Hotel exterior" data-full="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1200&h=800&fit=crop">
                                        </div>
                                        <div class="metropole-modal-thumb" data-index="1">
                                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=80&h=60&fit=crop" alt="Hotel lobby" data-full="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=1200&h=800&fit=crop">
                                        </div>
                                        <div class="metropole-modal-thumb" data-index="2">
                                            <img src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=80&h=60&fit=crop" alt="Hotel room" data-full="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=1200&h=800&fit=crop">
                                        </div>
                                        <div class="metropole-modal-thumb" data-index="3">
                                            <img src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=80&h=60&fit=crop" alt="Hotel restaurant" data-full="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=1200&h=800&fit=crop">
                                        </div>
                                        <div class="metropole-modal-thumb" data-index="4">
                                            <img src="https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=80&h=60&fit=crop" alt="Hotel pool" data-full="https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=1200&h=800&fit=crop">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </metropole-gallery>
            </div>
            
            <div class="product__info-wrapper">
                <h1 class="product__title">Around Georgia's Roads: 9 Day Jeep Tour</h1>
                <div class="product__price">0.00 GEL</div>
                <div class="product__description">
                    <p>Experience the breathtaking landscapes and rich culture of Georgia on this incredible 9-day jeep adventure. From ancient monasteries to stunning mountain vistas, this tour offers an unforgettable journey through one of the world's most beautiful countries.</p>
                    
                    <h3>Tour Highlights:</h3>
                    <ul>
                        <li>Professional Guide - Expert local guide with extensive knowledge</li>
                        <li>All Inclusive - Transportation, meals, and entrance fees included</li>
                        <li>Small Groups - Intimate experience with small group sizes</li>
                    </ul>
                </div>
                
                <div class="product__form">
                    <button class="btn" type="button">Book Now</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class MetropoleGallery extends HTMLElement {
            constructor() {
                super();
                this.modal = this.querySelector('.metropole-modal');
                this.modalImage = this.querySelector('.metropole-modal-image');
                this.modalThumbs = this.querySelectorAll('.metropole-modal-thumb');
                this.currentCounter = this.querySelector('.current');
                this.currentIndex = 0;
                this.images = [];
                
                // Collect all images
                this.modalThumbs.forEach((thumb) => {
                    const img = thumb.querySelector('img');
                    this.images.push({
                        src: img.dataset.full,
                        alt: img.alt
                    });
                });
                
                this.bindEvents();
            }
            
            bindEvents() {
                // Gallery items click
                this.querySelectorAll('[data-index]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const index = parseInt(e.currentTarget.dataset.index);
                        this.openModal(index);
                    });
                });
                
                // Modal controls
                this.querySelector('.metropole-modal-close')?.addEventListener('click', () => {
                    this.closeModal();
                });
                
                this.querySelector('.metropole-modal-prev')?.addEventListener('click', () => {
                    this.prevImage();
                });
                
                this.querySelector('.metropole-modal-next')?.addEventListener('click', () => {
                    this.nextImage();
                });
                
                // Thumbnail clicks
                this.modalThumbs.forEach((thumb, index) => {
                    thumb.addEventListener('click', () => {
                        this.showImage(index);
                    });
                });
                
                // Close on backdrop click
                this.modal?.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        this.closeModal();
                    }
                });
                
                // Keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (!this.modal.classList.contains('active')) return;
                    
                    switch(e.key) {
                        case 'Escape':
                            this.closeModal();
                            break;
                        case 'ArrowLeft':
                            this.prevImage();
                            break;
                        case 'ArrowRight':
                            this.nextImage();
                            break;
                    }
                });
            }
            
            openModal(index = 0) {
                this.currentIndex = index;
                this.showImage(index);
                this.modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
            
            closeModal() {
                this.modal.classList.remove('active');
                document.body.style.overflow = '';
            }
            
            showImage(index) {
                if (index < 0 || index >= this.images.length) return;
                
                this.currentIndex = index;
                this.modalImage.src = this.images[index].src;
                this.modalImage.alt = this.images[index].alt;
                
                // Update counter
                if (this.currentCounter) {
                    this.currentCounter.textContent = index + 1;
                }
                
                // Update active thumbnail
                this.modalThumbs.forEach((thumb, i) => {
                    thumb.classList.toggle('active', i === index);
                });
                
                // Scroll active thumbnail into view
                const activeThumb = this.modalThumbs[index];
                if (activeThumb) {
                    activeThumb.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
                }
            }
            
            nextImage() {
                const nextIndex = (this.currentIndex + 1) % this.images.length;
                this.showImage(nextIndex);
            }
            
            prevImage() {
                const prevIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
                this.showImage(prevIndex);
            }
        }
        
        customElements.define('metropole-gallery', MetropoleGallery);
    </script>
</body>
</html>
